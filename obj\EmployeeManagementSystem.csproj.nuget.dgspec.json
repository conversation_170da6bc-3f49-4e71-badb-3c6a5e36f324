{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj", "projectName": "EmployeeManagementSystem", "projectPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"AForge": {"target": "Package", "version": "[2.2.5, )"}, "AForge.Video.DirectShow": {"target": "Package", "version": "[2.2.5, )"}, "Accord": {"target": "Package", "version": "[3.8.0, )"}, "Accord.Imaging": {"target": "Package", "version": "[3.8.0, )"}, "Accord.Vision": {"target": "Package", "version": "[3.8.0, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "Hardware.Info": {"target": "Package", "version": "[101.0.1, )"}, "Microsoft.VisualBasic": {"target": "Package", "version": "[10.3.0, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.Extensions": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.11.0.20250507, )"}, "ScottPlot.WinForms": {"target": "Package", "version": "[4.1.68, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}