-- Script للتحقق من البيانات الحالية وتشخيص المشكلة
-- تشغيل هذا في SQL Server Management Studio

PRINT '=== 1. جميع المستخدمين ==='
SELECT 
    UserId,
    Username,
    FullName,
    UserType,
    DepartmentId,
    IsActive
FROM Users
ORDER BY UserId;

PRINT '=== 2. جميع الأقسام ==='
SELECT 
    DepartmentId,
    DepartmentName,
    DepartmentCode,
    ManagerUserId,
    IsActive
FROM Departments
ORDER BY DepartmentId;

PRINT '=== 3. مدراء الأقسام ==='
SELECT 
    u.UserId,
    u.Username,
    u.FullName,
    u.UserType,
    u.DepartmentId as UserDepartmentId,
    d.DepartmentId as ManagedDepartmentId,
    d.DepartmentName as ManagedDepartmentName
FROM Users u
LEFT JOIN Departments d ON d.ManagerUserId = u.UserId
WHERE u.UserType IN ('مدير قسم', 'مدير القسم')
ORDER BY u.UserId;

PRINT '=== 4. اختبار المستخدم محمد ==='
DECLARE @UserId INT = (SELECT UserId FROM Users WHERE Username = 'محمد');

SELECT 
    'بيانات المستخدم محمد:' as Info,
    UserId,
    Username,
    FullName,
    UserType,
    DepartmentId
FROM Users 
WHERE UserId = @UserId;

SELECT 
    'القسم الذي يديره محمد:' as Info,
    DepartmentId,
    DepartmentName,
    ManagerUserId
FROM Departments 
WHERE ManagerUserId = @UserId AND IsActive = 1;

SELECT 
    'المستخدمين في القسم الذي يديره محمد:' as Info,
    u.UserId,
    u.Username,
    u.FullName,
    u.UserType
FROM Users u
WHERE u.DepartmentId = (
    SELECT DepartmentId 
    FROM Departments 
    WHERE ManagerUserId = @UserId AND IsActive = 1
)
ORDER BY u.UserId;

PRINT '=== 5. اختبار استعلامات الكود ==='

-- اختبار GetManagedDepartmentId
SELECT 
    'GetManagedDepartmentId للمستخدم محمد:' as Test,
    DepartmentId 
FROM Departments 
WHERE ManagerUserId = @UserId AND IsActive = 1;

-- اختبار GetUsersByDepartment
DECLARE @ManagedDeptId INT = (
    SELECT DepartmentId 
    FROM Departments 
    WHERE ManagerUserId = @UserId AND IsActive = 1
);

IF @ManagedDeptId IS NOT NULL
BEGIN
    SELECT 
        'GetUsersByDepartment للقسم ' + CAST(@ManagedDeptId AS VARCHAR) + ':' as Test,
        U.UserId, U.Username, U.FullName, U.UserType, U.DepartmentId, D.DepartmentName
    FROM Users U
    LEFT JOIN Departments D ON U.DepartmentId = D.DepartmentId
    WHERE U.DepartmentId = @ManagedDeptId
    ORDER BY U.UserId;
END
ELSE
BEGIN
    PRINT 'المستخدم محمد لا يدير أي قسم!';
END

PRINT '=== 6. تشخيص المشاكل المحتملة ==='

-- التحقق من وجود مستخدمين بدون أقسام
SELECT 
    'مستخدمين بدون أقسام:' as Issue,
    UserId, Username, FullName, UserType
FROM Users 
WHERE DepartmentId IS NULL AND UserType != 'مدير'
ORDER BY UserId;

-- التحقق من وجود أقسام بدون مدراء
SELECT 
    'أقسام بدون مدراء:' as Issue,
    DepartmentId, DepartmentName, DepartmentCode
FROM Departments 
WHERE ManagerUserId IS NULL AND IsActive = 1
ORDER BY DepartmentId;

-- التحقق من مدراء أقسام لا ينتمون لأقسام
SELECT 
    'مدراء أقسام لا ينتمون لأقسام:' as Issue,
    u.UserId, u.Username, u.FullName, u.UserType, u.DepartmentId
FROM Users u
WHERE u.UserType IN ('مدير قسم', 'مدير القسم')
AND u.DepartmentId IS NULL
ORDER BY u.UserId;
