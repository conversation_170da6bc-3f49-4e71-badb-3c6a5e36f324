# دليل إعداد نظام الصلاحيات لمدراء الأقسام

## المشكلة التي تم حلها
كان مدير القسم يرى جميع المستخدمين في النظام بدلاً من رؤية موظفي قسمه فقط.

## الحل المطبق

### 1. تحديد العلاقة الصحيحة
النظام يستخدم جدول `Departments` مع حقل `ManagerUserId` لتحديد مدير كل قسم:

```sql
-- جدول الأقسام
Departments:
- DepartmentId (معرف القسم)
- DepartmentName (اسم القسم)  
- ManagerUserId (معرف مدير القسم)
- IsActive (نشط)

-- جدول المستخدمين
Users:
- UserId (معرف المستخدم)
- Username (اسم المستخدم)
- UserType (نوع المستخدم: "مدير" أو "مدير القسم" أو "مستخدم")
- DepartmentId (معرف القسم الذي ينتمي إليه المستخدم)
```

### 2. خطوات الإعداد المطلوبة

#### أ) إنشاء قسم جديد
1. اذهب إلى إدارة الأقسام
2. أضف قسم جديد (مثل: "قسم المحاسبة")
3. احفظ القسم (سيكون `ManagerUserId` فارغ في البداية)

#### ب) إنشاء مدير القسم
1. اذهب إلى إدارة المستخدمين
2. أضف مستخدم جديد:
   - نوع المستخدم: "مدير القسم"
   - القسم: اختر القسم المناسب
3. احفظ المستخدم

#### ج) ربط مدير القسم بقسمه
```sql
-- مثال: تعيين المستخدم رقم 5 كمدير للقسم رقم 2
UPDATE Departments 
SET ManagerUserId = 5 
WHERE DepartmentId = 2;
```

### 3. النتيجة المتوقعة
بعد الإعداد الصحيح:

✅ **مدير القسم سيرى فقط:**
- المستخدمين المنتمين لقسمه
- قائمة فارغة إذا لم يكن هناك مستخدمين في قسمه بعد

✅ **مدير القسم لن يستطيع:**
- رؤية مستخدمين من أقسام أخرى
- إنشاء مستخدمين من نوع "مدير"
- تعيين مستخدمين لأقسام أخرى

✅ **المدير العام سيرى:**
- جميع المستخدمين في النظام
- جميع الأقسام

### 4. رسائل التنبيه
إذا لم يتم تعيين مدير القسم بشكل صحيح، ستظهر رسالة:
"لم يتم تعيين قسم لك كمدير. يرجى التواصل مع المدير العام."

### 5. اختبار النظام
1. سجل دخول بحساب المدير العام
2. تأكد من وجود قسم وتعيين مدير له
3. سجل خروج وادخل بحساب مدير القسم
4. اذهب إلى إدارة المستخدمين
5. يجب أن ترى فقط مستخدمي قسمك

## ملاحظات مهمة
- تأكد من أن `ManagerUserId` في جدول `Departments` يشير إلى المستخدم الصحيح
- تأكد من أن `UserType` للمستخدم هو "مدير القسم" بالضبط
- تأكد من أن القسم نشط (`IsActive = 1`)
