-- Script لإصلاح بيانات الأقسام ومدراء الأقسام
-- تشغيل هذا الـ script في SQL Server Management Studio

-- 1. عرض الوضع الحالي
PRINT '=== الوضع الحالي ==='
SELECT 
    'المستخدم محمد:' as Info,
    UserId, Username, FullName, UserType, DepartmentId
FROM Users 
WHERE Username = 'محمد';

SELECT 
    'الأقسام:' as Info,
    DepartmentId, DepartmentName, ManagerUserId
FROM Departments 
WHERE DepartmentId IN (7, 8);

-- 2. الحلول المقترحة:

-- الحل الأول: جعل محمد مديراً للقسم الذي ينتمي إليه (القسم 8)
PRINT '=== الحل الأول: تعيين محمد كمدير للقسم 8 ==='
UPDATE Departments 
SET ManagerUserId = 19 
WHERE DepartmentId = 8;

-- التحقق من النتيجة
SELECT 
    'بعد التحديث:' as Info,
    DepartmentId, DepartmentName, ManagerUserId
FROM Departments 
WHERE DepartmentId = 8;

-- 3. إضافة مستخدمين آخرين للقسم 8 للاختبار (اختياري)
-- يمكنك إضافة مستخدمين جدد للقسم 8 لاختبار الفلترة

-- مثال لإضافة مستخدم جديد للقسم 8:
/*
INSERT INTO Users (Username, FullName, Password, UserType, DepartmentId, IsActive)
VALUES ('موظف1', 'أحمد علي', '1234', 'مستخدم', 8, 1);

INSERT INTO Users (Username, FullName, Password, UserType, DepartmentId, IsActive)
VALUES ('موظف2', 'فاطمة محمد', '1234', 'مستخدم', 8, 1);
*/

-- 4. التحقق النهائي
PRINT '=== التحقق النهائي ==='
SELECT 
    'مدير القسم:' as Info,
    u.UserId, u.Username, u.FullName, u.UserType, u.DepartmentId,
    d.DepartmentName
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.DepartmentId
WHERE u.Username = 'محمد';

SELECT 
    'القسم المُدار:' as Info,
    d.DepartmentId, d.DepartmentName, d.ManagerUserId,
    u.Username as ManagerUsername, u.FullName as ManagerName
FROM Departments d
LEFT JOIN Users u ON d.ManagerUserId = u.UserId
WHERE d.ManagerUserId = 19;

SELECT 
    'المستخدمين في القسم 8:' as Info,
    u.UserId, u.Username, u.FullName, u.UserType
FROM Users u
WHERE u.DepartmentId = 8
ORDER BY u.UserId;

-- 5. اختبار الاستعلام المستخدم في الكود
PRINT '=== اختبار استعلام GetManagedDepartmentId ==='
SELECT 
    'القسم المُدار للمستخدم 19:' as Info,
    DepartmentId 
FROM Departments 
WHERE ManagerUserId = 19 AND IsActive = 1;

PRINT '=== اختبار استعلام GetUsersByDepartment ==='
SELECT 
    'المستخدمين في القسم المُدار:' as Info,
    U.UserId, U.Username, U.FullName, U.UserType, U.DepartmentId, D.DepartmentName
FROM Users U
LEFT JOIN Departments D ON U.DepartmentId = D.DepartmentId
WHERE U.DepartmentId = (
    SELECT DepartmentId 
    FROM Departments 
    WHERE ManagerUserId = 19 AND IsActive = 1
)
ORDER BY U.UserId;
