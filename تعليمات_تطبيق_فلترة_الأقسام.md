# تعليمات تطبيق فلترة الأقسام على Form1

## 🎯 الهدف
جعل مدير القسم يرى فقط موظفي قسمه في نموذج إدارة الموظفين (Form1).

## 📝 التغييرات المطلوبة في Form1.cs

### 1. إضافة متغير المستخدم الحالي
```csharp
// في بداية الكلاس، أضف هذا المتغير:
private User? currentUser = null;
```

### 2. إضافة Constructor جديد
```csharp
public Form1(User currentUser) : this()
{
    this.currentUser = currentUser;
    System.Diagnostics.Debug.WriteLine($"Form1 - تم استلام المستخدم: {currentUser?.FullName} (نوع: {currentUser?.UserType})");
    
    // إعادة تحميل الموظفين مع الفلترة
    LoadEmployees();
}
```

### 3. تعديل دالة LoadEmployees
ابحث عن هذا السطر:
```csharp
var employees = await Task.Run(() =>
{
    return string.IsNullOrEmpty(searchTerm) ?
        DatabaseHelper.GetAllEmployees() :
        DatabaseHelper.SearchEmployees(searchTerm);
});
```

واستبدله بـ:
```csharp
var employees = await Task.Run(() => EmployeeDepartmentHelper.GetFilteredEmployees(currentUser, searchTerm));
```

### 4. تحديث عنوان النافذة
بعد السطر:
```csharp
UpdateNoDocumentsLabel();
```

أضف:
```csharp
// تحديث عنوان النافذة
this.Text = EmployeeDepartmentHelper.GetWindowTitle(currentUser, employees.Rows.Count);
```

### 5. تعديل دالة البحث SearchAndLoadEmployeesAsync
ابحث عن هذا السطر:
```csharp
var result = await Task.Run(() => DatabaseHelper.SearchEmployees(searchText));
```

واستبدله بـ:
```csharp
var result = await Task.Run(() => EmployeeDepartmentHelper.GetFilteredEmployees(currentUser, searchText));
```

## 🔧 تحديث MainForm.cs

في دالة `BtnManageEmployees_Click`، ابحث عن:
```csharp
OpenForm(new Form1());
```

واستبدله بـ:
```csharp
OpenForm(new Form1(CurrentUser));
```

## 🧪 اختبار النظام

بعد تطبيق التغييرات:

1. **شغل البرنامج**
2. **سجل دخول بحساب "محمد" (مدير القسم)**
3. **اذهب إلى إدارة الموظفين**
4. **ستجد أنه يعرض فقط موظفي قسمه**

### النتيجة المتوقعة:
- ✅ مدير القسم يرى فقط موظفي قسمه
- ✅ البحث يعمل ضمن قسمه فقط
- ✅ عنوان النافذة يوضح رقم القسم وعدد الموظفين
- ✅ المدير العام يرى جميع الموظفين

### رسائل التتبع:
ستظهر في Output Window رسائل مثل:
```
Form1 - تم استلام المستخدم: محمد المحمدي (نوع: مدير القسم)
EmployeeDepartmentHelper - فلترة موظفي القسم 9
```

## 📊 ملفات الدعم

تم إنشاء ملف `EmployeeDepartmentHelper.cs` الذي يحتوي على:
- `GetFilteredEmployees()` - فلترة الموظفين حسب نوع المستخدم
- `GetEmployeesByDepartment()` - جلب موظفي قسم معين
- `SearchEmployeesInDepartment()` - البحث في موظفي قسم معين
- `GetWindowTitle()` - تحديد عنوان النافذة المناسب

## 🚨 ملاحظات مهمة

1. **تأكد من وجود ربط صحيح** بين جدول `Employees` و `Users` عبر `EmployeeCode = UserId`
2. **تأكد من أن مدير القسم معين بشكل صحيح** في جدول `Departments`
3. **إذا لم تظهر النتائج المتوقعة**، تحقق من رسائل التتبع في Output Window

## 🔍 استكشاف الأخطاء

إذا لم يعمل النظام:
1. تحقق من رسائل Debug في Output Window
2. تأكد من أن `ManagerUserId` في جدول `Departments` يشير للمستخدم الصحيح
3. تأكد من أن `DepartmentId` في جدول `Users` صحيح
4. تأكد من أن `EmployeeCode` في جدول `Employees` يطابق `UserId` في جدول `Users`
