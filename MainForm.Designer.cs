namespace EmployeeManagementSystem
{
    partial class MainForm
    {
        private System.ComponentModel.IContainer components = null;
        private ToolStrip toolStrip;
        private ToolStripButton btnHome;
        private ToolStripButton btnDashboardStrip; // إضافة زر لوحة المعلومات
        private ToolStripButton btnManageEmployees;
        private ToolStripButton btnVacations;
        private ToolStripDropDownButton toolStripButtonCourses;
        private ToolStripMenuItem btnCourseManagement;
        private ToolStripMenuItem btnCourseAttendance;
        private ToolStripMenuItem btnCourseEvaluation;
        private ToolStripMenuItem btnCourseNotifications;
        private ToolStripMenuItem btnCourseDetails;
        private ToolStripMenuItem btnCourseStatistics;
        
        // أزرار القوائم الفرعية الجديدة
        private ToolStripDropDownButton btnAttendanceMenu;
        private ToolStripMenuItem btnAttendance;
        private ToolStripMenuItem btnAttendanceReport;
        
        private ToolStripDropDownButton btnWorkManagementMenu;
        private ToolStripMenuItem btnMonthlyOccurrences;
        private ToolStripMenuItem btnWorkPeriods;
        private ToolStripMenuItem btnGroupWorkPeriods;
        private ToolStripButton btnDepartments;
        private ToolStripButton btnActivityMonitor;
        private ToolStripButton btnSettings;
        private ToolStripButton Activation;
        private ToolStripButton AboutForm;
        private ToolStripButton btnNotifications;
        private ToolStripButton btnContacts;
        private Label lblNotificationCount;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripButton btnLogout;
        private Panel mainPanel;
        private System.Windows.Forms.Button btnDashboard;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            toolStrip = new ToolStrip();
            btnHome = new ToolStripButton();
            btnDashboardStrip = new ToolStripButton();
            btnUsers = new ToolStripButton();
            btnManageEmployees = new ToolStripButton();
            btnVacations = new ToolStripButton();
            toolStripButtonCourses = new ToolStripDropDownButton();
            btnCourseManagement = new ToolStripMenuItem();
            btnCourseAttendance = new ToolStripMenuItem();
            btnCourseEvaluation = new ToolStripMenuItem();
            btnCourseNotifications = new ToolStripMenuItem();
            btnCourseDetails = new ToolStripMenuItem();
            btnCourseStatistics = new ToolStripMenuItem();
            btnDepartments = new ToolStripButton();
            btnActivityMonitor = new ToolStripButton();
            btnWorkManagementMenu = new ToolStripDropDownButton();
            btnMonthlyOccurrences = new ToolStripMenuItem();
            btnGroupWorkPeriods = new ToolStripMenuItem();
            btnWorkPeriods = new ToolStripMenuItem();
            btnAttendanceMenu = new ToolStripDropDownButton();
            btnAttendance = new ToolStripMenuItem();
            btnAttendanceReport = new ToolStripMenuItem();
            btnContacts = new ToolStripButton();
            btnNotifications = new ToolStripButton();
            Activation = new ToolStripButton();
            btnSettings = new ToolStripButton();
            AboutForm = new ToolStripButton();
            toolStripSeparator1 = new ToolStripSeparator();
            btnLogout = new ToolStripButton();
            lblNotificationCount = new Label();
            mainPanel = new Panel();
            btnDashboard = new Button();
            toolStrip.SuspendLayout();
            mainPanel.SuspendLayout();
            SuspendLayout();
            // 
            // toolStrip
            // 
            toolStrip.AutoSize = false;
            toolStrip.BackColor = Color.FromArgb(45, 66, 91);
            toolStrip.Dock = DockStyle.Right;
            toolStrip.GripStyle = ToolStripGripStyle.Hidden;
            toolStrip.Items.AddRange(new ToolStripItem[] { btnHome, btnDashboardStrip, btnUsers, btnManageEmployees, btnVacations, toolStripButtonCourses, btnDepartments, btnActivityMonitor, btnWorkManagementMenu, btnAttendanceMenu, btnContacts, btnNotifications, Activation, btnSettings, AboutForm, toolStripSeparator1, btnLogout });
            toolStrip.Location = new Point(999, 0);
            toolStrip.Name = "toolStrip";
            toolStrip.RightToLeft = RightToLeft.Yes;
            toolStrip.Size = new Size(185, 661);
            toolStrip.TabIndex = 0;
            toolStrip.Text = "toolStrip1";
            // 
            // btnHome
            // 
            btnHome.AutoSize = false;
            btnHome.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnHome.ForeColor = Color.White;
            btnHome.Image = Properties.Resources.home_32px;
            btnHome.ImageScaling = ToolStripItemImageScaling.None;
            btnHome.Name = "btnHome";
            btnHome.RightToLeft = RightToLeft.No;
            btnHome.Size = new Size(136, 40);
            btnHome.Text = "الرئيسية";
            btnHome.Click += BtnHome_Click;
            // 
            // btnDashboardStrip
            // 
            btnDashboardStrip.AutoSize = false;
            btnDashboardStrip.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDashboardStrip.ForeColor = Color.White;
            btnDashboardStrip.Image = Properties.Resources.system_task_32px;
            btnDashboardStrip.ImageScaling = ToolStripItemImageScaling.None;
            btnDashboardStrip.Name = "btnDashboardStrip";
            btnDashboardStrip.RightToLeft = RightToLeft.No;
            btnDashboardStrip.Size = new Size(165, 40);
            btnDashboardStrip.Text = "لوحة المعلومات";
            btnDashboardStrip.Click += BtnDashboardStrip_Click;
            // 
            // btnUsers
            // 
            btnUsers.AutoSize = false;
            btnUsers.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUsers.ForeColor = Color.White;
            btnUsers.Image = Properties.Resources.management_32px;
            btnUsers.ImageScaling = ToolStripItemImageScaling.None;
            btnUsers.Name = "btnUsers";
            btnUsers.RightToLeft = RightToLeft.No;
            btnUsers.Size = new Size(175, 40);
            btnUsers.Text = "إدارة المستخدمين";
            btnUsers.Click += BtnUsers_Click;
            // 
            // btnManageEmployees
            // 
            btnManageEmployees.AutoSize = false;
            btnManageEmployees.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnManageEmployees.ForeColor = Color.White;
            btnManageEmployees.Image = Properties.Resources.user_groups_32px;
            btnManageEmployees.ImageScaling = ToolStripItemImageScaling.None;
            btnManageEmployees.Name = "btnManageEmployees";
            btnManageEmployees.RightToLeft = RightToLeft.No;
            btnManageEmployees.Size = new Size(165, 40);
            btnManageEmployees.Text = "إدارة الموظفين";
            btnManageEmployees.ToolTipText = "إدارة الموظفين";
            btnManageEmployees.Click += BtnManageEmployees_Click;
            // 
            // btnVacations
            // 
            btnVacations.AutoSize = false;
            btnVacations.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnVacations.ForeColor = Color.White;
            btnVacations.Image = Properties.Resources.traveler_32px;
            btnVacations.ImageScaling = ToolStripItemImageScaling.None;
            btnVacations.Name = "btnVacations";
            btnVacations.RightToLeft = RightToLeft.No;
            btnVacations.Size = new Size(150, 40);
            btnVacations.Text = "إدارة الإجازات";
            btnVacations.Click += BtnVacations_Click;
            // 
            // toolStripButtonCourses
            // 
            toolStripButtonCourses.AutoSize = false;
            toolStripButtonCourses.DropDownItems.AddRange(new ToolStripItem[] { btnCourseManagement, btnCourseAttendance, btnCourseEvaluation, btnCourseNotifications, btnCourseDetails, btnCourseStatistics });
            toolStripButtonCourses.Font = new Font("Cairo", 12F, FontStyle.Bold);
            toolStripButtonCourses.ForeColor = Color.White;
            toolStripButtonCourses.Image = Properties.Resources.classroom_32px;
            toolStripButtonCourses.ImageScaling = ToolStripItemImageScaling.None;
            toolStripButtonCourses.Name = "toolStripButtonCourses";
            toolStripButtonCourses.RightToLeft = RightToLeft.No;
            toolStripButtonCourses.Size = new Size(165, 40);
            toolStripButtonCourses.Text = "أدارة الدورات";
            toolStripButtonCourses.TextAlign = ContentAlignment.MiddleRight;
            // 
            // btnCourseManagement
            // 
            btnCourseManagement.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseManagement.Image = Properties.Resources.classroom_32px;
            btnCourseManagement.Name = "btnCourseManagement";
            btnCourseManagement.RightToLeft = RightToLeft.No;
            btnCourseManagement.Size = new Size(189, 30);
            btnCourseManagement.Text = "إدارة الدورات";
            btnCourseManagement.Click += BtnCourseManagement_Click;
            // 
            // btnCourseAttendance
            // 
            btnCourseAttendance.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseAttendance.Image = Properties.Resources.checked_checkbox_32px;
            btnCourseAttendance.Name = "btnCourseAttendance";
            btnCourseAttendance.RightToLeft = RightToLeft.No;
            btnCourseAttendance.Size = new Size(189, 30);
            btnCourseAttendance.Text = "حضور الدورات";
            btnCourseAttendance.Click += BtnCourseAttendance_Click;
            // 
            // btnCourseEvaluation
            // 
            btnCourseEvaluation.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseEvaluation.Image = Properties.Resources.rating_32px;
            btnCourseEvaluation.Name = "btnCourseEvaluation";
            btnCourseEvaluation.RightToLeft = RightToLeft.No;
            btnCourseEvaluation.Size = new Size(189, 30);
            btnCourseEvaluation.Text = "تقييم الدورات";
            btnCourseEvaluation.Click += BtnCourseEvaluation_Click;
            // 
            // btnCourseNotifications
            // 
            btnCourseNotifications.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseNotifications.Image = Properties.Resources.alarm_32px;
            btnCourseNotifications.Name = "btnCourseNotifications";
            btnCourseNotifications.RightToLeft = RightToLeft.No;
            btnCourseNotifications.Size = new Size(189, 30);
            btnCourseNotifications.Text = "إشعارات الدورات";
            btnCourseNotifications.Click += BtnCourseNotifications_Click;
            // 
            // btnCourseDetails
            // 
            btnCourseDetails.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseDetails.Image = Properties.Resources.info_32px;
            btnCourseDetails.Name = "btnCourseDetails";
            btnCourseDetails.RightToLeft = RightToLeft.No;
            btnCourseDetails.Size = new Size(189, 30);
            btnCourseDetails.Text = "تفاصيل الدورات";
            btnCourseDetails.Click += BtnCourseDetails_Click;
            // 
            // btnCourseStatistics
            // 
            btnCourseStatistics.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCourseStatistics.Image = Properties.Resources.chart_increasing_32px;
            btnCourseStatistics.Name = "btnCourseStatistics";
            btnCourseStatistics.RightToLeft = RightToLeft.No;
            btnCourseStatistics.Size = new Size(189, 30);
            btnCourseStatistics.Text = "إحصائيات الدورات";
            btnCourseStatistics.Click += BtnCourseStatistics_Click;
            // 
            // btnDepartments
            // 
            btnDepartments.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnDepartments.ForeColor = Color.White;
            btnDepartments.Name = "btnDepartments";
            btnDepartments.Size = new Size(183, 34);
            btnDepartments.Text = "ادارة الاقسام";
            btnDepartments.Click += btnDepartments_Click_1;
            // 
            // btnActivityMonitor
            // 
            btnActivityMonitor.AutoSize = false;
            btnActivityMonitor.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnActivityMonitor.ForeColor = Color.White;
            btnActivityMonitor.Image = Properties.Resources.system_task_32px;
            btnActivityMonitor.ImageScaling = ToolStripItemImageScaling.None;
            btnActivityMonitor.Name = "btnActivityMonitor";
            btnActivityMonitor.RightToLeft = RightToLeft.No;
            btnActivityMonitor.Size = new Size(175, 40);
            btnActivityMonitor.Text = "أدارة الأنشطة";
            btnActivityMonitor.TextAlign = ContentAlignment.MiddleRight;
            btnActivityMonitor.Click += BtnActivityMonitor_Click;
            // 
            // btnWorkManagementMenu
            // 
            btnWorkManagementMenu.AutoSize = false;
            btnWorkManagementMenu.DropDownItems.AddRange(new ToolStripItem[] { btnMonthlyOccurrences, btnGroupWorkPeriods, btnWorkPeriods });
            btnWorkManagementMenu.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnWorkManagementMenu.ForeColor = Color.White;
            btnWorkManagementMenu.Image = Properties.Resources.calendar_31_32px;
            btnWorkManagementMenu.ImageScaling = ToolStripItemImageScaling.None;
            btnWorkManagementMenu.Name = "btnWorkManagementMenu";
            btnWorkManagementMenu.RightToLeft = RightToLeft.No;
            btnWorkManagementMenu.Size = new Size(165, 40);
            btnWorkManagementMenu.Text = "إدارة العمل";
            btnWorkManagementMenu.TextAlign = ContentAlignment.MiddleRight;
            // 
            // btnMonthlyOccurrences
            // 
            btnMonthlyOccurrences.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnMonthlyOccurrences.Image = Properties.Resources.calendar_31_32px;
            btnMonthlyOccurrences.Name = "btnMonthlyOccurrences";
            btnMonthlyOccurrences.RightToLeft = RightToLeft.No;
            btnMonthlyOccurrences.Size = new Size(202, 30);
            btnMonthlyOccurrences.Text = "الوقوعات الشهرية";
            btnMonthlyOccurrences.Click += BtnMonthlyOccurrences_Click;
            // 
            // btnGroupWorkPeriods
            // 
            btnGroupWorkPeriods.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnGroupWorkPeriods.Image = Properties.Resources.crowd_32px;
            btnGroupWorkPeriods.Name = "btnGroupWorkPeriods";
            btnGroupWorkPeriods.RightToLeft = RightToLeft.No;
            btnGroupWorkPeriods.Size = new Size(202, 30);
            btnGroupWorkPeriods.Text = "مجموعات العمل";
            btnGroupWorkPeriods.Click += BtnGroupWorkPeriods_Click;
            // 
            // btnWorkPeriods
            // 
            btnWorkPeriods.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnWorkPeriods.Image = Properties.Resources.time_32px;
            btnWorkPeriods.Name = "btnWorkPeriods";
            btnWorkPeriods.RightToLeft = RightToLeft.No;
            btnWorkPeriods.Size = new Size(202, 30);
            btnWorkPeriods.Text = "فترات العمل";
            btnWorkPeriods.Click += BtnWorkPeriods_Click;
            // 
            // btnAttendanceMenu
            // 
            btnAttendanceMenu.AutoSize = false;
            btnAttendanceMenu.DropDownItems.AddRange(new ToolStripItem[] { btnAttendance, btnAttendanceReport });
            btnAttendanceMenu.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAttendanceMenu.ForeColor = Color.White;
            btnAttendanceMenu.Image = Properties.Resources.checked_checkbox_32px;
            btnAttendanceMenu.ImageScaling = ToolStripItemImageScaling.None;
            btnAttendanceMenu.Name = "btnAttendanceMenu";
            btnAttendanceMenu.RightToLeft = RightToLeft.No;
            btnAttendanceMenu.Size = new Size(166, 40);
            btnAttendanceMenu.Text = "أدارة الحضور";
            btnAttendanceMenu.TextAlign = ContentAlignment.MiddleRight;
            // 
            // btnAttendance
            // 
            btnAttendance.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnAttendance.Image = Properties.Resources.checked_checkbox_32px;
            btnAttendance.Name = "btnAttendance";
            btnAttendance.RightToLeft = RightToLeft.No;
            btnAttendance.Size = new Size(180, 30);
            btnAttendance.Text = "الحضور والغياب";
            btnAttendance.Click += BtnAttendance_Click;
            // 
            // btnAttendanceReport
            // 
            btnAttendanceReport.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnAttendanceReport.Image = Properties.Resources.hand_with_pen_32px;
            btnAttendanceReport.Name = "btnAttendanceReport";
            btnAttendanceReport.RightToLeft = RightToLeft.No;
            btnAttendanceReport.Size = new Size(180, 30);
            btnAttendanceReport.Text = "تقارير الحضور";
            btnAttendanceReport.Click += BtnAttendanceReport_Click;
            // 
            // btnContacts
            // 
            btnContacts.AutoSize = false;
            btnContacts.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnContacts.ForeColor = Color.White;
            btnContacts.Image = Properties.Resources.phone_32px;
            btnContacts.ImageScaling = ToolStripItemImageScaling.None;
            btnContacts.Name = "btnContacts";
            btnContacts.RightToLeft = RightToLeft.No;
            btnContacts.Size = new Size(165, 40);
            btnContacts.Text = "دليل الاتصال";
            btnContacts.Click += BtnContacts_Click;
            // 
            // btnNotifications
            // 
            btnNotifications.AutoSize = false;
            btnNotifications.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnNotifications.ForeColor = Color.White;
            btnNotifications.Image = Properties.Resources.alarm_32px;
            btnNotifications.ImageScaling = ToolStripItemImageScaling.None;
            btnNotifications.Name = "btnNotifications";
            btnNotifications.RightToLeft = RightToLeft.No;
            btnNotifications.Size = new Size(136, 40);
            btnNotifications.Text = " الاشعارات";
            btnNotifications.Click += BtnNotifications_Click;
            // 
            // Activation
            // 
            Activation.AutoSize = false;
            Activation.Font = new Font("Cairo", 12F, FontStyle.Bold);
            Activation.ForeColor = Color.White;
            Activation.Image = Properties.Resources.key_2_32px;
            Activation.ImageScaling = ToolStripItemImageScaling.None;
            Activation.Name = "Activation";
            Activation.RightToLeft = RightToLeft.No;
            Activation.Size = new Size(136, 50);
            Activation.Text = "التفعيل";
            Activation.ToolTipText = "التفعيل";
            Activation.Click += Activation_Click_1;
            // 
            // btnSettings
            // 
            btnSettings.AutoSize = false;
            btnSettings.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSettings.ForeColor = Color.White;
            btnSettings.Image = Properties.Resources.settings_32px;
            btnSettings.ImageScaling = ToolStripItemImageScaling.None;
            btnSettings.Name = "btnSettings";
            btnSettings.RightToLeft = RightToLeft.No;
            btnSettings.Size = new Size(136, 50);
            btnSettings.Text = "الإعدادات";
            btnSettings.ToolTipText = "الإعدادات";
            btnSettings.Click += BtnSettings_Click;
            // 
            // AboutForm
            // 
            AboutForm.AutoSize = false;
            AboutForm.Font = new Font("Cairo", 12F, FontStyle.Bold);
            AboutForm.ForeColor = Color.White;
            AboutForm.Image = Properties.Resources.info_32px;
            AboutForm.ImageScaling = ToolStripItemImageScaling.None;
            AboutForm.Name = "AboutForm";
            AboutForm.RightToLeft = RightToLeft.No;
            AboutForm.Size = new Size(138, 40);
            AboutForm.Text = "حول البرنامج";
            AboutForm.ToolTipText = "حول البرنامج";
            AboutForm.Click += AboutForm_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            toolStripSeparator1.Size = new Size(183, 6);
            // 
            // btnLogout
            // 
            btnLogout.AutoSize = false;
            btnLogout.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnLogout.ForeColor = Color.White;
            btnLogout.Image = Properties.Resources.delete_32px;
            btnLogout.ImageScaling = ToolStripItemImageScaling.None;
            btnLogout.Name = "btnLogout";
            btnLogout.RightToLeft = RightToLeft.No;
            btnLogout.Size = new Size(145, 40);
            btnLogout.Text = "تسجيل الخروج";
            btnLogout.ToolTipText = "تسجيل الخروج";
            btnLogout.Click += BtnLogout_Click;
            // 
            // lblNotificationCount
            // 
            lblNotificationCount.AutoSize = true;
            lblNotificationCount.BackColor = Color.Red;
            lblNotificationCount.BorderStyle = BorderStyle.FixedSingle;
            lblNotificationCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblNotificationCount.ForeColor = Color.White;
            lblNotificationCount.Location = new Point(1150, 5);
            lblNotificationCount.Name = "lblNotificationCount";
            lblNotificationCount.Padding = new Padding(5, 2, 5, 2);
            lblNotificationCount.Size = new Size(20, 20);
            lblNotificationCount.TabIndex = 0;
            lblNotificationCount.Text = "0";
            lblNotificationCount.TextAlign = ContentAlignment.MiddleCenter;
            lblNotificationCount.Visible = false;
            // 
            // mainPanel
            // 
            mainPanel.Controls.Add(btnDashboard);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Size = new Size(999, 661);
            mainPanel.TabIndex = 1;
            // 
            // btnDashboard
            // 
            btnDashboard.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDashboard.BackColor = Color.FromArgb(45, 66, 91);
            btnDashboard.Enabled = false;
            btnDashboard.FlatStyle = FlatStyle.Flat;
            btnDashboard.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDashboard.ForeColor = Color.White;
            btnDashboard.Location = new Point(750, 27);
            btnDashboard.Name = "btnDashboard";
            btnDashboard.Size = new Size(199, 50);
            btnDashboard.TabIndex = 0;
            btnDashboard.Text = "لوحة المعلومات📊";
            btnDashboard.UseVisualStyleBackColor = false;
            btnDashboard.Visible = false;
            btnDashboard.Click += btnDashboard_Click;
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1184, 661);
            Controls.Add(mainPanel);
            Controls.Add(toolStrip);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Name = "MainForm";
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "نظام إدارة الموظفين";
            WindowState = FormWindowState.Maximized;
            FormClosed += MainForm_FormClosed;
            Load += MainForm_Load;
            toolStrip.ResumeLayout(false);
            toolStrip.PerformLayout();
            mainPanel.ResumeLayout(false);
            ResumeLayout(false);
        }
        private ToolStripButton btnUsers;
    }
}