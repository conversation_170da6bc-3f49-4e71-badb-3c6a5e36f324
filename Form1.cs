#nullable enable

using System;
using System.Windows.Forms;
using System.Drawing;
using System.IO;
using System.Data;
using System.Diagnostics;
using System.Drawing.Imaging;
using System.Text;
using System.Linq;
using System.Drawing.Printing;
using System.Data.SqlClient;
using ClosedXML.Excel;
//using WIA;
using System.Management;
using EmployeeManagementSystem.LoadingGui;
using System.Reflection;

namespace EmployeeManagementSystem
{
    public partial class Form1 : Form
    {
        private string? currentEmployeeCode = null;
        private int lastAddedEmployeeId = -1;
        private string? currentPhotoPath;
        private CancellationTokenSource? cts;
        private bool isShowingSampleData = false;
        private System.Threading.Timer? searchTimer; private void ChkSelectAll_CheckedChanged(object? sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgvEmployees.Rows)
            {
                row.Selected = chkSelectAll.Checked;
            }
        }

        public Form1()
        {
            InitializeComponent();
            LoadEmployees();
            // إعداد DataGridView للتحديد المتعدد
            dgvEmployees.MultiSelect = true;
            dgvEmployees.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // جعل جميع العناصر ظاهرة وقابلة للتحرير في وضع التصميم
            foreach (Control control in this.Controls)
            {
                if (control is TextBox textBox)
                {
                    textBox.Visible = true;
                    textBox.Enabled = true;
                    textBox.RightToLeft = RightToLeft.Yes;
                }
                else if (control is DateTimePicker dateTimePicker)
                {
                    dateTimePicker.Visible = true;
                    dateTimePicker.Enabled = true;
                    dateTimePicker.RightToLeft = RightToLeft.Yes;
                }
                else if (control is Button button)
                {
                    button.Visible = true;
                    button.Enabled = true;
                    button.FlatStyle = FlatStyle.Flat;
                    button.BackColor = Color.FromArgb(45, 66, 91);
                    button.ForeColor = Color.White;
                    if (button == btnClear)
                    {
                        button.BackColor = Color.FromArgb(192, 0, 0);
                    }
                }
                else if (control is Label label)
                {
                    label.Visible = true;
                    label.RightToLeft = RightToLeft.Yes;
                }
                else if (control is ComboBox comboBox)
                {
                    comboBox.Visible = true;
                    comboBox.Enabled = true;
                    comboBox.RightToLeft = RightToLeft.Yes;
                }
            }

            // إعداد DataGridView مع تحسينات الأداء
            dgvEmployees.RightToLeft = RightToLeft.Yes;
            dgvEmployees.EnableHeadersVisualStyles = false;
            dgvEmployees.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 66, 91);
            dgvEmployees.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvEmployees.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dgvEmployees.DefaultCellStyle.Font = new Font("Cairo", 12F);
            dgvEmployees.DefaultCellStyle.SelectionBackColor = Color.FromArgb(87, 115, 153);
            dgvEmployees.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvEmployees.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(237, 243, 247);
            dgvEmployees.BackgroundColor = Color.White;
            dgvEmployees.BorderStyle = BorderStyle.None;
            dgvEmployees.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvEmployees.GridColor = Color.FromArgb(223, 230, 233);
            dgvEmployees.RowTemplate.Height = 35;
            dgvEmployees.Visible = true;
            dgvEmployees.Dock = DockStyle.Bottom;
            dgvEmployees.BringToFront();

            // تحسينات الأداء
            dgvEmployees.RowHeadersVisible = false;
            dgvEmployees.AllowUserToAddRows = false;
            dgvEmployees.AllowUserToDeleteRows = false;
            dgvEmployees.AllowUserToResizeRows = false;
            dgvEmployees.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // تفعيل DoubleBuffered لتحسين الأداء
            typeof(DataGridView).InvokeMember("DoubleBuffered",
                BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.SetProperty,
                null, dgvEmployees, new object[] { true });



            // تعيين خصائص مربع المعاينة
            previewBox.SizeMode = PictureBoxSizeMode.Zoom;
            previewBox.BorderStyle = BorderStyle.FixedSingle;
            previewBox.BackColor = Color.White;

            // لا نحمل البيانات تلقائياً لتحسين الأداء
            InitializeEmptyGrid();

            // تحميل عدد محدود من الموظفين كمعاينة
            LoadSampleEmployees();

            // إضافة event handler لمربع البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // تطبيق الثيم المحفوظ
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);

        }
        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            // التأكد من رغبة المستخدم في تحميل جميع البيانات
            if (MessageBox.Show("هل تريد تحميل جميع بيانات الموظفين؟\n\nقد يستغرق هذا وقتاً إذا كان العدد كبيراً.",
                "تأكيد التحميل", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                txtSearch.Clear();
                ClearForm();
                previewBox.Image = null;

                var loadingForm = new LoadingForm(this);
                loadingForm.Show();
                loadingForm.Refresh();

                try
                {
                    // أولاً نحصل على البيانات في الخلفية
                    var employees = await Task.Run(() => DatabaseHelper.GetAllEmployees());

                    // بعدين نحدث الواجهة (يجب من خيط الواجهة)
                    dgvEmployees.DataSource = null;
                    dgvEmployees.Columns.Clear();
                    dgvEmployees.DataSource = employees;

                    // إعداد الأعمدة بعد تعيين البيانات
                    SetupGridColumns();

                    UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "لا توجد بيانات");

                    // تحديث عنوان النافذة
                    this.Text = $"نظام إدارة الموظفين - عرض كامل ({employees.Rows.Count} موظف)";
                    isShowingSampleData = false;
                }
                finally
                {
                    loadingForm.Close();
                    previewBox.Image = Properties.Resources.picture;
                    previewBox.Visible = true;
                }
            }
        }



        private void UpdateNoDocumentsLabel()
        {
            //lbl_NoDocuments.Visible = dgvEmployees.Rows.Count == 0;
            UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "لا توجد بيانات");
        }

        private void InitializeEmptyGrid()
        {
            // تهيئة الجدول فارغ مع رسالة ترحيب
            dgvEmployees.DataSource = null;
            dgvEmployees.Columns.Clear();
            UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                "⏳ جاري تحميل معاينة الموظفين...");
        }

        private void SetupGridColumns()
        {
            // تعيين خصائص الأعمدة
            if (dgvEmployees.Columns.Count > 0)
            {
                // ضبط ارتفاع رؤوس الأعمدة
                dgvEmployees.ColumnHeadersHeight = 50;
                dgvEmployees.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

                foreach (DataGridViewColumn col in dgvEmployees.Columns)
                {
                    if (col != null)
                    {
                        // ضبط العرض الأدنى والأقصى للأعمدة
                        col.MinimumWidth = 100;
                        col.DividerWidth = 1;

                        switch (col.DataPropertyName)
                        {
                            case "EmployeeCode":
                                col.HeaderText = "كود الموظف";
                                col.Width = 100;
                                break;
                            case "Category":
                                col.HeaderText = "الصنف";
                                col.Width = 120;
                                break;
                            case "Name":
                                col.HeaderText = "الاسم";
                                col.Width = 200;
                                break;
                            case "MotherName":
                                col.HeaderText = "اسم الأم";
                                col.Width = 150;
                                break;
                            case "MaritalStatus":
                                col.HeaderText = "الحالة الزوجية";
                                col.Width = 120;
                                break;
                            case "WifeName":
                                col.HeaderText = "اسم الزوج/ـة";
                                col.Width = 150;
                                break;
                            case "Province":
                                col.HeaderText = "المحافظة";
                                col.Width = 120;
                                break;
                            case "Nationality":
                                col.HeaderText = "الجنسية";
                                col.Width = 120;
                                break;
                            case "IdentityNumber":
                                col.HeaderText = "رقم الهوية";
                                col.Width = 150;
                                break;
                            case "DateOfBirth":
                                col.HeaderText = "تاريخ الميلاد";
                                col.Width = 150;
                                break;
                            case "EducationLevel":
                                col.HeaderText = "المستوى التعليمي";
                                col.Width = 150;
                                break;
                            case "ElectoralNumber":
                                col.HeaderText = "الرقم الانتخابي";
                                col.Width = 150;
                                break;
                            case "StartDate":
                                col.HeaderText = "تاريخ الامر الاداري";
                                col.Width = 150;
                                break;
                            case "AdministrativeOrder":
                                col.HeaderText = "الأمر الإداري";
                                col.Width = 150;
                                break;
                            case "StatisticalNumber":
                                col.HeaderText = "الرقم الإحصائي";
                                col.Width = 150;
                                break;
                            case "KeyCardNumber":
                                col.HeaderText = "رقم الكي كارد";
                                col.Width = 150;
                                break;
                            case "PhoneNumber":
                                col.HeaderText = "رقم الهاتف";
                                col.Width = 150;
                                break;
                            case "BadgeNumber":
                                col.HeaderText = "رقم الباج";
                                col.Width = 120;
                                break;
                            case "BadgeExpiryDate":
                                col.HeaderText = "تاريخ انتهاء الباج";
                                col.Width = 150;
                                break;
                            case "PhotoPath":
                            case "DocumentPath":
                            case "PhotoData":
                                col.Visible = false;
                                col.Width = 0;
                                break;
                        }

                        // تنسيق إضافي لرؤوس الأعمدة
                        col.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                        col.HeaderCell.Style.Padding = new Padding(0, 5, 0, 5);
                    }
                }

                // تطبيق التنسيق
                foreach (DataGridViewColumn col in dgvEmployees.Columns)
                {
                    col.SortMode = DataGridViewColumnSortMode.NotSortable;
                }

                dgvEmployees.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
                dgvEmployees.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;

                // إصلاح مشكلة ارتفاع الصف الأول
                dgvEmployees.RowTemplate.Height = 35;
                foreach (DataGridViewRow row in dgvEmployees.Rows)
                {
                    row.Height = 35;
                }
            }
        }

        private async void LoadSampleEmployees()
        {
            try
            {
                // تحميل أول 20 موظف كمعاينة
                var sampleEmployees = await Task.Run(() => DatabaseHelper.GetSampleEmployees(20));

                if (sampleEmployees != null && sampleEmployees.Rows.Count > 0)
                {
                    dgvEmployees.DataSource = sampleEmployees;
                    SetupGridColumns();

                    // إخفاء رسالة "لا توجد بيانات" لأن هناك بيانات
                    lbl_NoDocuments.Visible = false;

                    // إضافة تلميح بصري في شريط العنوان
                    this.Text = $"نظام إدارة الموظفين - معاينة ({sampleEmployees.Rows.Count} موظف)";
                    isShowingSampleData = true;
                }
                else
                {
                    UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                        "لا توجد بيانات موظفين في النظام");
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، نعرض الرسالة الأساسية
                UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                    "مرحباً بك في نظام إدارة الموظفين 👋\n\n" +
                    "📋 للبحث عن موظف محدد: اكتب اسمه أو رقمه واضغط 'بحث'\n" +
                    "📊 لعرض جميع الموظفين: اضغط 'تحديث'");
            }
        }

        private async void LoadEmployees(string searchTerm = "")
        {
            try
            {
                // إظهار مؤشر التحميل
                this.Cursor = Cursors.WaitCursor;

                var employees = await Task.Run(() =>
                {
                    return string.IsNullOrEmpty(searchTerm) ?
                        DatabaseHelper.GetAllEmployees() :
                        DatabaseHelper.SearchEmployees(searchTerm);
                });

                dgvEmployees.DataSource = null;
                dgvEmployees.Columns.Clear();
                dgvEmployees.DataSource = employees;

                // إعداد الأعمدة بعد تعيين البيانات
                SetupGridColumns();

                UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "لا توجد بيانات");

                UpdateNoDocumentsLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }


        }




        private void LoadEmployeeImage(DataGridViewRow row)
        {
            if (row != null)
            {
                var photoPathCell = row.Cells["PhotoPath"];
                string? relativePhotoPath = photoPathCell?.Value?.ToString();

                if (!string.IsNullOrEmpty(relativePhotoPath))
                {
                    // بناء المسار الكامل باستخدام مجلد التطبيق الحالي + المسار النسبي
                    string fullPhotoPath = Path.Combine(Application.StartupPath, relativePhotoPath);

                    if (File.Exists(fullPhotoPath))
                    {
                        try
                        {
                            using (var stream = new FileStream(fullPhotoPath, FileMode.Open, FileAccess.Read))
                            {
                                previewBox.Image?.Dispose();
                                previewBox.Image = Image.FromStream(stream);
                                previewBox.Visible = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            // في حال وجود خطأ في القراءة
                            previewBox.Image?.Dispose();
                            previewBox.Image = null;
                            previewBox.Visible = false;
                            MessageBox.Show("خطأ في عرض الصورة: " + ex.Message);
                        }
                    }
                    else
                    {
                        // الملف غير موجود
                        previewBox.Image?.Dispose();
                        previewBox.Image = null;
                        previewBox.Visible = false;
                    }
                }
                else
                {
                    // لا يوجد مسار صورة
                    previewBox.Image?.Dispose();
                    previewBox.Image = null;
                    previewBox.Visible = false;
                }
            }
        }

        private void dgvEmployees_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var row = dgvEmployees.Rows[e.RowIndex];
                LoadEmployeeImage(row);

                // تحميل بيانات الموظف
                currentEmployeeCode = row.Cells["كود الموظف"].Value?.ToString();
                txtName.Text = row.Cells["الاسم"].Value?.ToString() ?? "";
                txtMotherName.Text = row.Cells["اسم الأم"].Value?.ToString() ?? "";
                txtProvince.Text = row.Cells["المحافظة"].Value?.ToString() ?? "";
                txtNationality.Text = row.Cells["الجنسية"].Value?.ToString() ?? "";
                txtIdentityNumber.Text = row.Cells["رقم الهوية"].Value?.ToString() ?? "";
                txtAdminOrder.Text = row.Cells["الأمر الإداري"].Value?.ToString() ?? "";
                txtStatisticalNumber.Text = row.Cells["الرقم الإحصائي"].Value?.ToString() ?? "";
                txtKeyCard.Text = row.Cells["رقم الكي كارد"].Value?.ToString() ?? "";
                txtPhone.Text = row.Cells["رقم الهاتف"].Value?.ToString() ?? "";
                txtCategory.Text = row.Cells["الصنف"].Value?.ToString() ?? "";
                txtBadgeNumber.Text = row.Cells["رقم الباج"].Value?.ToString() ?? "";

                // الحالة الاجتماعية - cmbMaritalStatus
                string maritalStatusValue = row.Cells["الحالة الاجتماعية"].Value?.ToString() ?? "";
                if (cmbMaritalStatus.Items.Contains(maritalStatusValue))
                {
                    cmbMaritalStatus.SelectedItem = maritalStatusValue;
                }
                else
                {
                    cmbMaritalStatus.SelectedIndex = -1; // أو تعيين قيمة افتراضية
                }

                // اسم الزوج/ة - txtWifeName
                txtWifeName.Text = row.Cells["اسم الزوج/ـة"].Value?.ToString() ?? "";

                // الرقم الانتخابي - txtElectoralNumber
                txtElectoralNumber.Text = row.Cells["الرقم الانتخابي"].Value?.ToString() ?? "";

                // المستوى التعليمي - cmbEducationLevel (محسن للتوافق مع تطبيق الويب)
                string educationLevelValue = row.Cells["المستوى التعليمي"].Value?.ToString()?.Trim() ?? "";

                // البحث عن تطابق دقيق أولاً
                bool found = false;
                foreach (var item in cmbEducationLevel.Items)
                {
                    if (item.ToString().Trim().Equals(educationLevelValue, StringComparison.OrdinalIgnoreCase))
                    {
                        cmbEducationLevel.SelectedItem = item;
                        found = true;
                        break;
                    }
                }

                // إذا لم يوجد تطابق دقيق، نحاول البحث الجزئي للحالات الخاصة
                if (!found && !string.IsNullOrEmpty(educationLevelValue))
                {
                    // قائمة بالقيم المشكلة وبدائلها
                    var problematicValues = new Dictionary<string, string>
                    {
                        { "دبلوم", "دبلوم" },
                        { "بكالوريوس", "بكالوريوس" },
                        { "ماجستير", "ماجستير" }
                    };

                    foreach (var kvp in problematicValues)
                    {
                        if (educationLevelValue.Contains(kvp.Key) || kvp.Key.Contains(educationLevelValue))
                        {
                            foreach (var item in cmbEducationLevel.Items)
                            {
                                if (item.ToString().Trim().Equals(kvp.Value, StringComparison.OrdinalIgnoreCase))
                                {
                                    cmbEducationLevel.SelectedItem = item;
                                    found = true;
                                    break;
                                }
                            }
                            if (found) break;
                        }
                    }
                }

                // إذا لم يوجد أي تطابق، نترك الحقل فارغاً
                if (!found)
                {
                    cmbEducationLevel.SelectedIndex = -1;
                    // تسجيل للتشخيص
                    Console.WriteLine($"⚠️ لم يتم العثور على تطابق للمستوى التعليمي: '{educationLevelValue}'");
                }



                dtpDateOfBirth.Value = ParseDateSafely(row.Cells["تاريخ الميلاد"].Value);
                dtpStartDate.Value = ParseDateSafely(row.Cells["تاريخ الامر الاداري"].Value);
                dtpBadgeExpiry.Value = ParseDateSafely(row.Cells["تاريخ انتهاء الباج"].Value);

                var photoPathCell = row.Cells["PhotoPath"];
                if (photoPathCell?.Value != null && !string.IsNullOrEmpty(photoPathCell.Value.ToString()))
                {
                    currentPhotoPath = photoPathCell.Value.ToString();
                    string fullPhotoPath = Path.Combine(Application.StartupPath, currentPhotoPath);

                    if (File.Exists(fullPhotoPath))
                    {
                        using (var stream = new FileStream(fullPhotoPath, FileMode.Open, FileAccess.Read))
                        {
                            previewBox.Image?.Dispose();
                            previewBox.Image = Image.FromStream(stream);
                        }
                    }
                    else
                    {
                        // الصورة غير موجودة على القرص → نحاول تحميلها من القاعدة
                        if (int.TryParse(row.Cells["كود الموظف"].Value?.ToString(), out int employeeCode))
                        {
                            byte[]? photoData = DatabaseHelper.GetEmployeePhotoData(employeeCode);
                            if (photoData != null && photoData.Length > 0)
                            {
                                using (var ms = new MemoryStream(photoData))
                                {
                                    Image img = Image.FromStream(ms);
                                    previewBox.Image?.Dispose();
                                    previewBox.Image = new Bitmap(img);

                                    // إعادة حفظ الصورة في القرص (اختياري لكن يُنصح به)
                                    try
                                    {
                                        string savePath = fullPhotoPath;
                                        Directory.CreateDirectory(Path.GetDirectoryName(savePath)!);
                                        img.Save(savePath);
                                    }
                                    catch { /* تجاهل الخطأ إذا فشل الحفظ */ }
                                }
                            }
                            else
                            {
                                previewBox.Image = null;
                            }
                        }
                        else
                        {
                            previewBox.Image = null;
                        }
                    }
                }
                else
                {
                    previewBox.Image = null;
                }

            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                int recordCount = await Task.Run(() => GetEmployeeCount());

                if (recordCount >= 5)
                {
                    MessageBox.Show(
                        "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                        "المميزات المتاحة:\n" +
                        "- يمكنك استخدام جميع خصائص النظام\n" +
                        "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                        "للاستمرار بدون قيود:\n" +
                        "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                        "2. أدخل رمز التفعيل الخاص بك\n" +
                        "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                        "شكراً لاختيارك منتجاتنا",
                        "النسخة التجريبية - ميزات مقيدة",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                    return;
                }
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return;
            }
            if (previewBox.Image == null || string.IsNullOrEmpty(currentPhotoPath))
            {
                MessageBox.Show("يرجى إضافة ملف للموظف ليتم حفظ البيانات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            // التعامل مع النسخ والملفات خارج الـ UI thread
            string? photoPath = null;
            byte[]? photoData = null;

            if (previewBox.Image != null && !string.IsNullOrEmpty(currentPhotoPath))
            {
                string employeeName = txtName.Text.Trim();
                string fileName = Path.GetFileName(currentPhotoPath);
                string relativePath = Path.Combine("documents", employeeName, fileName);
                string destFullPath = Path.Combine(Application.StartupPath, relativePath);
                string destDir = Path.GetDirectoryName(destFullPath)!;

                await Task.Run(() =>
                {
                    if (!Directory.Exists(destDir))
                        Directory.CreateDirectory(destDir);

                    if (!File.Exists(destFullPath))
                        File.Copy(currentPhotoPath, destFullPath, true);
                });

                photoPath = relativePath;

                // هنا تضغط الصورة بعد نسخها
                photoData = DatabaseHelper.CompressImage(File.ReadAllBytes(destFullPath), 60L);
            }


            var employee = new Employee
            {
                EmployeeCode = currentEmployeeCode != null ? int.Parse(currentEmployeeCode) : 0,
                Name = txtName.Text,
                MotherName = txtMotherName.Text,
                Province = txtProvince.Text,
                Nationality = txtNationality.Text,
                IdentityNumber = txtIdentityNumber.Text,
                AdministrativeOrder = txtAdminOrder.Text,
                StatisticalNumber = txtStatisticalNumber.Text,
                KeyCardNumber = txtKeyCard.Text,
                PhoneNumber = txtPhone.Text,
                Category = txtCategory.Text,
                BadgeNumber = txtBadgeNumber.Text,
                DateOfBirth = dtpDateOfBirth.Value,
                StartDate = dtpStartDate.Value,
                BadgeExpiryDate = dtpBadgeExpiry.Value,
                MaritalStatus = cmbMaritalStatus.SelectedItem?.ToString() ?? "",
                WifeName = txtWifeName.Text,
                ElectoralNumber = txtElectoralNumber.Text,
                EducationLevel = cmbEducationLevel.SelectedItem?.ToString() ?? "",
                PhotoPath = photoPath,
                PhotoData = photoData // تضيف هذا الحقل
            };


            try
            {
                // تنفيذ العمليات على قاعدة البيانات في مهمة غير متزامنة
                if (string.IsNullOrEmpty(currentEmployeeCode))
                {
                    lastAddedEmployeeId = await Task.Run(() => DatabaseHelper.AddEmployee(employee));
                    ToastHelper.ShowAddToast();
                    MessageBox.Show("تم إضافة الموظف بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await Task.Run(() => DatabaseHelper.UpdateEmployee(employee));
                    ToastHelper.ShowEditToast();
                    MessageBox.Show("تم تحديث بيانات الموظف بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // إعادة تحميل البيانات وتحديث الواجهة يمكن تركها في الـ UI thread
                await ReloadEmployeesAsync();

                UpdateNoDocumentsLabel();
                ClearForm();

                //if (previewBox.Image != null)
                //{
                //    var currentImage = previewBox.Image;
                //    previewBox.Image = null;
                //    currentImage.Dispose();
                //}
                //previewBox.Visible = false;
                // فقط حرّر الصورة إذا لم تكن من الموارد (أي تم تحميلها من ملف خارجي)
                if (previewBox.Image != null && previewBox.Image != Properties.Resources.picture)
                {
                    previewBox.Image.Dispose();
                }

                previewBox.Image = Properties.Resources.picture;
                previewBox.Visible = true;

                currentPhotoPath = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

        }


        // دالة للحصول على عدد السجلات من قاعدة البيانات
        private int GetEmployeeCount()
        {
            int count = 0;
            string connectionString = ConStringHelper.GetConnectionString();

            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Employees";
                using (var command = new SqlCommand(query, connection))
                {
                    count = Convert.ToInt32(command.ExecuteScalar());
                }
            }
            return count;
        }

        private void btnAddPhoto_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم الموظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return;
            }

            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "صور|*.jpg;*.jpeg;*.png;*.gif;*.bmp";
                openFileDialog.Title = "اختيار صورة الموظف";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        string employeeName = txtName.Text.Trim();
                        string documentsPath = Path.Combine(Application.StartupPath, "documents");
                        string employeeFolderPath = Path.Combine(documentsPath, employeeName);

                        if (!Directory.Exists(employeeFolderPath))
                            Directory.CreateDirectory(employeeFolderPath);

                        string extension = Path.GetExtension(openFileDialog.FileName);
                        string fileName = GetUniqueFileName(employeeFolderPath, "photo", extension);
                        string destFullPath = Path.Combine(employeeFolderPath, fileName);

                        // حذف الصورة القديمة إن وُجدت
                        if (!string.IsNullOrEmpty(currentPhotoPath))
                        {
                            string oldFullPath = Path.Combine(Application.StartupPath, currentPhotoPath);
                            if (File.Exists(oldFullPath))
                                File.Delete(oldFullPath);
                        }

                        File.Copy(openFileDialog.FileName, destFullPath, true);

                        // حفظ المسار النسبي
                        currentPhotoPath = Path.Combine("documents", employeeName, fileName);
                        // بعد حفظ الصورة على القرص
                        currentPhotoPath = Path.Combine("documents", employeeName, fileName);

                        // تحويل الصورة إلى بايتات
                        //byte[] photoBytes = File.ReadAllBytes(destFullPath);
                        byte[] photoBytes = DatabaseHelper.CompressImage(File.ReadAllBytes(destFullPath), 60L);
                        // جلب كود الموظف
                        int employeeCode = DatabaseHelper.GetEmployeeCodeByName(employeeName);

                        // إذا لم يوجد كود الموظف، لا تعطي رسالة خطأ، فقط لا تحفظ في القاعدة الآن
                        if (employeeCode == 0)
                        {
                            // يمكن وضع الكود لحفظ الصورة محلياً فقط أو تجاهل الحفظ في قاعدة البيانات
                            // مثلاً:
                            // MessageBox.Show("لم يتم العثور على كود الموظف - الصورة لن تحفظ الآن في القاعدة");
                        }
                        else
                        {
                            // حفظ المسار والبايتات في القاعدة فقط إذا كود الموظف موجود
                            DatabaseHelper.UpdateEmployeePhoto(employeeCode, currentPhotoPath, photoBytes);
                        }


                        using (var image = Image.FromFile(destFullPath))
                        {
                            previewBox.Image?.Dispose();
                            previewBox.Image = new Bitmap(image);
                            previewBox.Visible = true;
                        }

                        MessageBox.Show("تم اضافة الصورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحميل الصورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        string GetUniqueFileName(string folderPath, string baseName, string extension)
        {
            string fileName = $"{baseName}{extension}";
            int count = 1;

            while (File.Exists(Path.Combine(folderPath, fileName)))
            {
                fileName = $"{baseName} ({count}){extension}";
                count++;
            }

            return fileName;
        }


        private void ClearForm()
        {
            currentEmployeeCode = null;
            txtName.Clear();
            txtMotherName.Clear();
            txtProvince.Clear();
            txtNationality.Clear();
            txtIdentityNumber.Clear();
            txtAdminOrder.Clear();
            txtStatisticalNumber.Clear();
            txtKeyCard.Clear();
            txtPhone.Clear();
            txtCategory.Clear();
            txtBadgeNumber.Clear();
            dtpDateOfBirth.Value = DateTime.Now;
            dtpStartDate.Value = DateTime.Now;
            dtpBadgeExpiry.Value = DateTime.Now;
            cmbMaritalStatus.SelectedIndex = -1;   // تفريغ الاختيار
            txtWifeName.Clear();
            txtElectoralNumber.Clear();
            cmbEducationLevel.SelectedIndex = -1;  // تفريغ الاختيار
        }

        private DateTime ParseDateSafely(object? value)
        {
            if (value != null && DateTime.TryParse(value.ToString(), out DateTime result))
            {
                return result;
            }
            return DateTime.Now;
        }
        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvEmployees.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string message = dgvEmployees.SelectedRows.Count == 1
                ? "هل أنت متأكد من حذف هذا الموظف وكافة ملفاته؟"
                : $"هل أنت متأكد من حذف {dgvEmployees.SelectedRows.Count} موظف وكافة ملفاتهم؟";

            if (MessageBox.Show(message, "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    foreach (DataGridViewRow row in dgvEmployees.SelectedRows)
                    {
                        int employeeId = Convert.ToInt32(row.Cells["كود الموظف"].Value);
                        string employeeName = row.Cells["الاسم"].Value.ToString() ?? "";
                        string employeeFolderPath = Path.Combine(Application.StartupPath, "documents", employeeName);

                        // حذف الموظف من قاعدة البيانات (تأكد أن هذه الدالة تحذف السجل كاملاً)
                        DatabaseHelper.DeleteEmployee(employeeId);

                        // حذف مجلد الموظف وجميع محتوياته
                        if (Directory.Exists(employeeFolderPath))
                        {
                            try
                            {
                                Directory.Delete(employeeFolderPath, true);
                            }
                            catch (IOException ioEx)
                            {
                                MessageBox.Show($"تعذر حذف ملفات الموظف {employeeName} بسبب أنها مفتوحة أو مستخدمة.\n{ioEx.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                            catch (UnauthorizedAccessException uaEx)
                            {
                                MessageBox.Show($"ليس لديك صلاحية حذف ملفات الموظف {employeeName}.\n{uaEx.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }

                    MessageBox.Show("تم حذف الموظفين وكافة ملفاتهم بنجاح", "نجاح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تفريغ وإخفاء PictureBox بشكل صحيح
                    //if (previewBox.Image != null)
                    //{
                    //    var currentImage = previewBox.Image;
                    //    previewBox.Image = null;
                    //    currentImage.Dispose();
                    //}
                    //previewBox.Visible = false;
                    // فقط حرّر الصورة إذا لم تكن من الموارد (أي تم تحميلها من ملف خارجي)
                    if (previewBox.Image != null && previewBox.Image != Properties.Resources.picture)
                    {
                        previewBox.Image.Dispose();
                    }

                    previewBox.Image = Properties.Resources.picture;
                    previewBox.Visible = true;

                    currentPhotoPath = null;

                    // تفريغ مربع التحديد
                    chkSelectAll.Checked = false;

                    // تحميل الموظفين وتفريغ النموذج بعد الحذف
                    ToastHelper.ShowDeleteToast();
                    await ReloadEmployeesAsync();

                    UpdateNoDocumentsLabel();
                    ClearForm();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء حذف الموظفين: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        private async Task ReloadEmployeesAsync()
        {
            var data = await Task.Run(() => DatabaseHelper.GetAllEmployees());
            Invoke(() => dgvEmployees.DataSource = data);
        }

        private void btnDocuments_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(currentEmployeeCode))
            {
                var employeeName = txtName.Text.Trim();

                // إنشاء مجلد للموظف إذا لم يكن موجوداً
                string documentsPath = Path.Combine(Application.StartupPath, "documents");
                string employeeFolderPath = Path.Combine(documentsPath, employeeName);

                if (!Directory.Exists(employeeFolderPath))
                {
                    Directory.CreateDirectory(employeeFolderPath);
                }

                var documentForm = new DocumentManagementForm(int.Parse(currentEmployeeCode), employeeName);
                documentForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnPrintSelected_Click(object sender, EventArgs e)
        {
            if (dgvEmployees.SelectedRows.Count > 0)
            {
                int selectedEmployeeId = Convert.ToInt32(dgvEmployees.SelectedRows[0].Cells["كود الموظف"].Value);
                PrintEmployeeDetails(selectedEmployeeId);
            }
            else
            {
                MessageBox.Show("الرجاء اختيار موظف للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void PrintEmployeeDetails(int employeeId)
        {
            try
            {
                string htmlFile = DatabaseHelper.GenerateEmployeeReport(employeeId);
                string fullPath = Path.Combine(Application.StartupPath, htmlFile);

                if (File.Exists(fullPath))
                {
                    var processInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fullPath,
                        UseShellExecute = true
                    };
                    System.Diagnostics.Process.Start(processInfo);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملف التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة معلومات الموظف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnPrintAll_Click(object sender, EventArgs e)
        {
            try
            {
                string htmlFile = DatabaseHelper.GenerateAllEmployeesHtml();
                string fullPath = Path.Combine(Application.StartupPath, htmlFile);

                if (File.Exists(fullPath))
                {
                    var processInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fullPath,
                        UseShellExecute = true
                    };
                    System.Diagnostics.Process.Start(processInfo);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملف التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة قائمة الموظفين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                int recordCount = await Task.Run(() => GetEmployeeCount());
                if (recordCount >= 5)
                {
                    MessageBox.Show(
                        "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                        "المميزات المتاحة:\n" +
                        "- يمكنك استخدام جميع خصائص النظام\n" +
                        "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                        "للاستمرار بدون قيود:\n" +
                        "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                        "2. أدخل رمز التفعيل الخاص بك\n" +
                        "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                        "شكراً لاختيارك منتجاتنا",
                        "النسخة التجريبية - ميزات مقيدة",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                    return;
                }
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return;
            }

            if (dgvEmployees.SelectedRows.Count > 0)
            {
                try
                {
                    var currentEmployeeId = int.Parse(currentEmployeeCode ?? "0");

                    // جلب بيانات الموظف الحالي من قاعدة البيانات (نفذها في Task.Run لو كانت بطيئة)
                    var existingEmployee = await Task.Run(() => DatabaseHelper.GetEmployeeById(currentEmployeeId));

                    string documentsPath = Path.Combine(Application.StartupPath, "documents");
                    string newEmployeeFolderPath = Path.Combine(documentsPath, txtName.Text.Trim());
                    string oldEmployeeFolderPath = Path.Combine(documentsPath, existingEmployee.Name ?? "");

                    // تشغيل عمليات الملفات خارج الـ UI thread
                    await Task.Run(() =>
                    {
                        // إذا تغير اسم الموظف، نغير اسم مجلد الملفات
                        if (existingEmployee.Name != txtName.Text.Trim() && Directory.Exists(oldEmployeeFolderPath))
                        {
                            if (!Directory.Exists(newEmployeeFolderPath))
                            {
                                Directory.Move(oldEmployeeFolderPath, newEmployeeFolderPath);
                            }
                            else
                            {
                                foreach (string file in Directory.GetFiles(oldEmployeeFolderPath))
                                {
                                    string fileName = Path.GetFileName(file);
                                    string destFile = Path.Combine(newEmployeeFolderPath, fileName);
                                    if (!File.Exists(destFile))
                                    {
                                        File.Move(file, destFile);
                                    }
                                }
                                Directory.Delete(oldEmployeeFolderPath, true);
                            }
                        }
                    });

                    // بناء مسار الصورة النسبي لو موجودة
                    string? photoPath = null;
                    if (previewBox.Image != null && !string.IsNullOrEmpty(currentPhotoPath))
                    {
                        photoPath = Path.Combine("documents", txtName.Text.Trim(), Path.GetFileName(currentPhotoPath));
                    }

                    // قراءة الصورة كـ byte[] لحفظها في قاعدة البيانات
                    byte[]? photoData = null;
                    if (!string.IsNullOrEmpty(currentPhotoPath))
                    {
                        string fullPhotoPath = Path.Combine(Application.StartupPath, currentPhotoPath);
                        if (File.Exists(fullPhotoPath))
                        {
                            //photoData = File.ReadAllBytes(fullPhotoPath);
                            photoData = DatabaseHelper.CompressImage(File.ReadAllBytes(fullPhotoPath), 60L);
                        }
                    }

                    var employee = new Employee
                    {
                        EmployeeCode = currentEmployeeId,
                        Name = txtName.Text,
                        MotherName = txtMotherName.Text,
                        Province = txtProvince.Text,
                        Nationality = txtNationality.Text,
                        IdentityNumber = txtIdentityNumber.Text,
                        AdministrativeOrder = txtAdminOrder.Text,
                        StatisticalNumber = txtStatisticalNumber.Text,
                        KeyCardNumber = txtKeyCard.Text,
                        PhoneNumber = txtPhone.Text,
                        Category = txtCategory.Text,
                        BadgeNumber = txtBadgeNumber.Text,
                        DateOfBirth = dtpDateOfBirth.Value,
                        StartDate = dtpStartDate.Value,
                        BadgeExpiryDate = dtpBadgeExpiry.Value,
                        MaritalStatus = cmbMaritalStatus.SelectedItem?.ToString() ?? "",
                        WifeName = txtWifeName.Text,
                        ElectoralNumber = txtElectoralNumber.Text,
                        EducationLevel = cmbEducationLevel.SelectedItem?.ToString() ?? "",
                        PhotoPath = photoPath,
                        PhotoData = photoData
                    };

                    // تحديث بيانات الموظف في قاعدة البيانات خارج الـ UI thread
                    await Task.Run(() => DatabaseHelper.UpdateEmployee(employee));

                    MessageBox.Show("تم تحديث بيانات الموظف بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ToastHelper.ShowEditToast();

                    // تحميل البيانات وتحديث الواجهة (هذه العملية يمكن أن تبقى في الـ UI thread)
                    await ReloadEmployeesAsync();

                    UpdateNoDocumentsLabel();
                    ClearForm();

                    //if (previewBox.Image != null)
                    //{
                    //    var currentImage = previewBox.Image;
                    //    previewBox.Image = null;
                    //    currentImage.Dispose();
                    //}
                    //previewBox.Visible = false;
                    // فقط حرّر الصورة إذا لم تكن من الموارد (أي تم تحميلها من ملف خارجي)
                    if (previewBox.Image != null && previewBox.Image != Properties.Resources.picture)
                    {
                        previewBox.Image.Dispose();
                    }

                    previewBox.Image = Properties.Resources.picture;
                    previewBox.Visible = true;

                    currentPhotoPath = null;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("الرجاء اختيار موظف للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }



        private void btnClear_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل أنت متأكد من رغبتك في إفراغ جميع الحقول؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                ClearForm();
                dgvEmployees.ClearSelection();
                previewBox.Image?.Dispose();
                previewBox.Image = null;
                currentPhotoPath = null;

                // إعادة تعيين الصورة الافتراضية وإظهارها
                previewBox.Image = Properties.Resources.picture;
                previewBox.Visible = true;
            }
        }



        private class FileItem
        {
            public string Description { get; set; } = "";
            public string FilePath { get; set; } = "";
            public override string ToString()
            {
                return Description;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "تقرير_بيانات الموظفين_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("بيانات الموظفين");

                            // Add headers
                            for (int i = 0; i < dgvEmployees.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dgvEmployees.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dgvEmployees.Rows.Count; i++)
                            {
                                for (int j = 0; j < dgvEmployees.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dgvEmployees.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dgvEmployees.Rows.Count + 1, dgvEmployees.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            // Adjust column widths
                            worksheet.Columns().AdjustToContents();

                            workbook.SaveAs(sfd.FileName);
                        }

                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btn_Scan_Click(object sender, EventArgs e)
        {
            //if (string.IsNullOrWhiteSpace(txtName.Text))
            //{
            //    MessageBox.Show("الرجاء إدخال اسم الموظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            //    txtName.Focus();
            //    return;
            //}
            //if (listOfScanner.SelectedIndex == -1)
            //{
            //    MessageBox.Show("الرجاء اختيار جهاز ماسح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            //    return;
            //}

            //Label lblInsidePreview = new()
            //{
            //    AutoSize = false,
            //    Height = 30,
            //    Dock = DockStyle.Bottom,
            //    TextAlign = ContentAlignment.MiddleCenter,
            //    Font = new Font("Cairo", 10, FontStyle.Bold),
            //    BackColor = Color.FromArgb(180, Color.White), // شفاف قليلاً
            //    ForeColor = Color.DarkBlue,
            //    Text = "...جاري المسح"
            //};

            //previewBox.Controls.Add(lblInsidePreview);
            //lblInsidePreview.BringToFront();
            //lblInsidePreview.Show();

            //CancellationTokenSource cts = new();
            //_ = Task.Run(async () =>
            //{
            //    int dotCount = 0;
            //    while (!cts.Token.IsCancellationRequested)
            //    {
            //        dotCount = (dotCount + 1) % 4;
            //        string dots = new('.', dotCount);
            //        lblInsidePreview.Invoke(() =>
            //        {
            //            lblInsidePreview.Text = $"{dots}جاري المسح ممكن ان تاخذ العملية يعض الوقت";
            //        });
            //        await Task.Delay(500);
            //    }
            //});

            //// تنفيذ عملية المسح في الخلفية
            //await Task.Run(() =>
            //{
            //    try
            //    {
            //        var deviceManager = new DeviceManager();
            //        Device? scanner = null;

            //        for (int i = 1; i <= deviceManager.DeviceInfos.Count; i++)
            //        {
            //            if (deviceManager.DeviceInfos[i].Type == WiaDeviceType.ScannerDeviceType)
            //            {
            //                scanner = deviceManager.DeviceInfos[i].Connect();
            //                break;
            //            }
            //        }

            //        if (scanner == null)
            //        {
            //            Invoke(() => MessageBox.Show("لم يتم العثور على جهاز ماسح ضوئي", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error));
            //            return;
            //        }

            //        Item scanItem = scanner.Items[1];
            //        ImageFile image = (ImageFile)scanItem.Transfer("{B96B3CAF-0728-11D3-9D7B-0000F81EF32E}");

            //        string employeeName = txtName.Invoke(() => txtName.Text.Trim());
            //        string documentsPath = Path.Combine(Application.StartupPath, "documents");
            //        Directory.CreateDirectory(documentsPath);

            //        string employeeFolderPath = Path.Combine(documentsPath, employeeName);
            //        Directory.CreateDirectory(employeeFolderPath);

            //        // حذف الصورة القديمة لو موجودة قبل حفظ الصورة الجديدة
            //        if (!string.IsNullOrEmpty(currentPhotoPath))
            //        {
            //            string oldFullPath = Path.Combine(Application.StartupPath, currentPhotoPath);
            //            if (File.Exists(oldFullPath))
            //            {
            //                try
            //                {
            //                    File.Delete(oldFullPath);
            //                }
            //                catch
            //                {
            //                    // يمكن تسجيل الخطأ أو تجاهله
            //                }
            //            }
            //        }

            //        string fileName = $"scan_{DateTime.Now:yyyyMMddHHmmss}.jpg";
            //        string destPath = Path.Combine(employeeFolderPath, fileName);

            //        image.SaveFile(destPath);
            //        // حفظ المسار النسبي فقط وليس المسار الكامل
            //        string relativePath = Path.Combine("documents", employeeName, fileName);
            //        currentPhotoPath = relativePath;

            //        int employeeCode = DatabaseHelper.GetEmployeeCodeByName(employeeName);

            //        byte[] rawImageBytes = File.ReadAllBytes(destPath);
            //        byte[] compressedImageBytes = DatabaseHelper.CompressImage(rawImageBytes, 60L);
            //        DatabaseHelper.UpdateEmployeePhoto(employeeCode, relativePath, compressedImageBytes);

            //        // عرض الصورة
            //        Invoke(() =>
            //        {
            //            string fullImagePath = Path.Combine(Application.StartupPath, currentPhotoPath);
            //            using var img = Image.FromFile(fullImagePath);
            //            previewBox.Image?.Dispose();
            //            previewBox.Image = new Bitmap(img);
            //            previewBox.Visible = true;

            //            MessageBox.Show("تم المسح والحفظ بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //        });
            //    }
            //    catch (Exception ex)
            //    {
            //        Invoke(() => MessageBox.Show($"خطأ أثناء المسح: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error));
            //    }
            //});

            //// إغلاق شريط التحميل
            //cts.Cancel();
            //lblInsidePreview.Invoke(() => previewBox.Controls.Remove(lblInsidePreview));
        }


        private async void button2_Click(object sender, EventArgs e)
        {
            if (previewBox.Controls["lblLoading"] != null) // اذا موجود مسبقاً نحذف
                previewBox.Controls.RemoveByKey("lblLoading");

            Label lblLoading = new()
            {
                Name = "lblLoading",
                Text = "جاري البحث عن أجهزة الماسح الضوئي",
                Dock = DockStyle.Bottom,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Cairo", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(180, Color.White),
                ForeColor = Color.DarkBlue,
            };

            previewBox.Controls.Add(lblLoading);
            lblLoading.BringToFront();
            lblLoading.Show();

            cts = new CancellationTokenSource();

            // حركة النقاط داخل اللابل
            _ = Task.Run(async () =>
            {
                int dotCount = 0;
                while (!cts.Token.IsCancellationRequested)
                {
                    dotCount = (dotCount + 1) % 4;
                    string dots = new('.', dotCount);
                    lblLoading.Invoke(() =>
                    {
                        lblLoading.Text = dots + " جاري البحث عن أجهزة الماسح الضوئي";
                    });
                    await Task.Delay(500);
                }
            });

            // تنفيذ عملية البحث عن الماسحات
            await Task.Run(() =>
            {
                this.Invoke((MethodInvoker)delegate
                {
                    LoadScanners();
                });
            });

            // إيقاف حركة النقاط وإخفاء الشريط
            cts.Cancel();
            lblLoading.Invoke(() => previewBox.Controls.Remove(lblLoading));
        }

        private void LoadScanners()
        {
            //listOfScanner.Items.Clear();
            //var deviceManager = new DeviceManager();

            //for (int i = 1; i <= deviceManager.DeviceInfos.Count; i++)
            //{
            //    if (deviceManager.DeviceInfos[i].Type == WiaDeviceType.ScannerDeviceType)
            //    {
            //        var deviceInfo = deviceManager.DeviceInfos[i];
            //        listOfScanner.Items.Add(deviceInfo.Properties["Name"].get_Value());
            //    }
            //}

            //if (listOfScanner.Items.Count > 0)
            //    listOfScanner.SelectedIndex = 0;
            //else
            //    MessageBox.Show("لا يوجد أجهزة ماسح متصلة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
        private void btnViewPhoto_Click(object sender, EventArgs e)
        {
            try
            {
                // جلب كود الموظف
                int employeeCode = DatabaseHelper.GetEmployeeCodeByName(txtName.Text.Trim());
                if (employeeCode == 0)
                {
                    MessageBox.Show("لم يتم العثور على كود الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // محاولة العرض من القرص أولاً
                string fullPhotoPath = Path.Combine(Application.StartupPath, currentPhotoPath ?? "");
                if (!string.IsNullOrEmpty(currentPhotoPath) && File.Exists(fullPhotoPath))
                {
                    Process.Start(new ProcessStartInfo("explorer", fullPhotoPath) { UseShellExecute = true });
                    return;
                }

                // إذا لم توجد الصورة على القرص، نحاول تحميلها من قاعدة البيانات
                byte[] photoData = DatabaseHelper.GetEmployeePhotoData(employeeCode);
                if (photoData == null || photoData.Length == 0)
                {
                    MessageBox.Show("لا توجد صورة محفوظة لهذا الموظف.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // حفظ الصورة مؤقتاً وعرضها
                string tempDir = Path.Combine(Application.StartupPath, "temp_photos");
                if (!Directory.Exists(tempDir))
                    Directory.CreateDirectory(tempDir);

                string tempFilePath = Path.Combine(tempDir, $"photo_{employeeCode}.jpg");
                File.WriteAllBytes(tempFilePath, photoData);

                Process.Start(new ProcessStartInfo("explorer", tempFilePath) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء عرض الصورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //private void btnViewPhoto_Click(object sender, EventArgs e)
        //{
        //    if (!string.IsNullOrEmpty(currentPhotoPath) && File.Exists(currentPhotoPath))
        //    {
        //        try
        //        {
        //            // افتح باستخدام مستعرض الملفات الافتراضي
        //            Process.Start(new ProcessStartInfo("explorer", currentPhotoPath) { UseShellExecute = true });
        //        }
        //        catch (Exception ex)
        //        {
        //            MessageBox.Show("تعذر فتح الصورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //        }
        //    }
        //    else
        //    {
        //        MessageBox.Show("لا توجد صورة لعرضها.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //    }
        //}
        private bool IsCameraConnected()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE Description LIKE '%camera%' OR Description LIKE '%video%'"))
                {
                    var devices = searcher.Get();
                    return devices.Count > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        private void btnOpenCamera_Click(object sender, EventArgs e)
        {
            //CameraForm cameraForm = new CameraForm();
            //cameraForm.OnImageCaptured += CameraForm_OnImageCaptured;
            //cameraForm.Show();
            if (!IsCameraConnected())
            {
                MessageBox.Show("الكامرة غير متصلة أو غير متوفرة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            CameraForm cameraForm = new CameraForm();
            cameraForm.OnImageCaptured += CameraForm_OnImageCaptured;
            cameraForm.Show();
        }
        private void CameraForm_OnImageCaptured(Bitmap capturedImage)
        {
            if (capturedImage == null) return;

            if (InvokeRequired)
            {
                Invoke(new Action(() => CameraForm_OnImageCaptured(capturedImage)));
                return;
            }

            try
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الموظف أولاً");
                    return;
                }

                string employeeName = txtName.Text.Trim();
                string documentsPath = Path.Combine(Application.StartupPath, "documents");
                if (!Directory.Exists(documentsPath))
                    Directory.CreateDirectory(documentsPath);

                string employeeFolderPath = Path.Combine(documentsPath, employeeName);
                if (!Directory.Exists(employeeFolderPath))
                    Directory.CreateDirectory(employeeFolderPath);

                string fileName = $"photo_{DateTime.Now:yyyyMMddHHmmss}.jpg";
                string destPath = Path.Combine(employeeFolderPath, fileName);

                // حذف الصورة القديمة إذا كانت موجودة
                if (!string.IsNullOrEmpty(currentPhotoPath))
                {
                    string oldFullPath = Path.Combine(Application.StartupPath, currentPhotoPath);
                    if (File.Exists(oldFullPath))
                        File.Delete(oldFullPath);
                }

                // حفظ الصورة
                capturedImage.Save(destPath, System.Drawing.Imaging.ImageFormat.Jpeg);

                // حفظ المسار النسبي فقط
                string relativePath = Path.Combine("documents", employeeName, fileName);
                currentPhotoPath = relativePath;

                previewBox.Image?.Dispose();
                previewBox.Image = new Bitmap(capturedImage);
                previewBox.Visible = true;

                MessageBox.Show("تم حفظ الصورة بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصورة: {ex.Message}");
            }
        }


        private void cmbMaritalStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbMaritalStatus.SelectedItem == null)
            {
                txtWifeName.Enabled = false;
                txtWifeName.Text = "";
                return;
            }

            if (cmbMaritalStatus.SelectedItem.ToString() == "متزوج/ـة")
            {
                txtWifeName.Enabled = true;
            }
            else
            {
                txtWifeName.Enabled = false;
                txtWifeName.Text = "";
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "لا توجد بيانات");
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // تنظيف الموارد
            searchTimer?.Dispose();
            base.OnFormClosed(e);
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            await SearchAndLoadEmployeesAsync();
            // فقط حرّر الصورة إذا لم تكن من الموارد (أي تم تحميلها من ملف خارجي)
            if (previewBox.Image != null && previewBox.Image != Properties.Resources.picture)
            {
                previewBox.Image.Dispose();
            }

            previewBox.Image = Properties.Resources.picture;
            previewBox.Visible = true;

        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // إلغاء أي Timer سابق
            searchTimer?.Dispose();

            // عندما يصبح مربع البحث فارغاً، أرجع للمعاينة
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                // فقط إذا لم نكن نعرض المعاينة بالفعل
                if (!isShowingSampleData)
                {
                    // إضافة تأخير بسيط لتجنب التحميل المتكرر
                    searchTimer = new System.Threading.Timer(async _ =>
                    {
                        await ReturnToSampleView();
                    }, null, 300, Timeout.Infinite); // تأخير 300 ميللي ثانية

                    // إظهار رسالة مؤقتة
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => {
                            UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "🔄 جاري استرجاع المعاينة...");
                        }));
                    }
                    else
                    {
                        UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments, "🔄 جاري استرجاع المعاينة...");
                    }
                }
            }
            else
            {
                // إذا كان هناك نص، فنحن لسنا في وضع المعاينة
                isShowingSampleData = false;
            }
        }

        private async Task ReturnToSampleView()
        {
            try
            {
                // تحميل المعاينة مرة أخرى
                var sampleEmployees = await Task.Run(() => DatabaseHelper.GetSampleEmployees(20));

                // التأكد من أن نحن في UI thread
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => UpdateUIWithSampleData(sampleEmployees)));
                }
                else
                {
                    UpdateUIWithSampleData(sampleEmployees);
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، نعرض رسالة بسيطة
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => {
                        UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                            "حدث خطأ في تحميل البيانات");
                        this.Text = "نظام إدارة الموظفين";
                    }));
                }
                else
                {
                    UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                        "حدث خطأ في تحميل البيانات");
                    this.Text = "نظام إدارة الموظفين";
                }
            }
        }

        private void UpdateUIWithSampleData(DataTable sampleEmployees)
        {
            if (sampleEmployees != null && sampleEmployees.Rows.Count > 0)
            {
                dgvEmployees.DataSource = sampleEmployees;
                SetupGridColumns();

                // إخفاء رسالة "لا توجد بيانات" لأن هناك بيانات
                lbl_NoDocuments.Visible = false;

                // تحديث عنوان النافذة
                this.Text = $"نظام إدارة الموظفين - معاينة ({sampleEmployees.Rows.Count} موظف)";
                isShowingSampleData = true;
            }
            else
            {
                UIHelper.ShowEmptyMessage(dgvEmployees, lbl_NoDocuments,
                    "لا توجد بيانات موظفين في النظام");
                this.Text = "نظام إدارة الموظفين";
                isShowingSampleData = false;
            }
        }

        private async void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;
                await SearchAndLoadEmployeesAsync();
            }
        }

        private async Task SearchAndLoadEmployeesAsync()
        {
            string searchText = txtSearch.Text.Trim();

            if (string.IsNullOrWhiteSpace(searchText))
            {
                MessageBox.Show("الرجاء كتابة شيء للبحث عنه.\n\nأو اضغط على 'تحديث' لعرض جميع الموظفين.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من الحد الأدنى لطول النص المدخل
            if (searchText.Length < 2)
            {
                MessageBox.Show("الرجاء كتابة حرفين على الأقل للبحث.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {
                var result = await Task.Run(() => DatabaseHelper.SearchEmployees(searchText));

                dgvEmployees.Invoke((MethodInvoker)delegate
                {
                    dgvEmployees.DataSource = null;
                    dgvEmployees.Columns.Clear();
                    dgvEmployees.DataSource = result;

                    // إعداد الأعمدة بعد تعيين البيانات
                    SetupGridColumns();

                    UpdateNoDocumentsLabel();

                    if (result == null || result.Rows.Count == 0)
                    {
                        MessageBox.Show("لم يتم العثور على أي موظف يطابق البحث.", "نتيجة البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.Text = "نظام إدارة الموظفين - لا توجد نتائج";
                    }
                    else
                    {
                        // إظهار عدد النتائج
                        MessageBox.Show($"تم العثور على {result.Rows.Count} موظف.", "نتيجة البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.Text = $"نظام إدارة الموظفين - نتائج البحث ({result.Rows.Count} موظف)";
                        isShowingSampleData = false;
                    }

                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm.Close();
            }
        }


    }
}
