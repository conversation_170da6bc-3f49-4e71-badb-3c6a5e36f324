{"version": 3, "targets": {"net6.0-windows7.0": {"Accord/3.8.0": {"type": "package", "compile": {"lib/netstandard2.0/Accord.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.dll": {"related": ".xml"}}, "build": {"build/Accord.targets": {}}}, "Accord.Imaging/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0", "Accord.Math": "3.8.0", "Accord.Statistics": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.Imaging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.Imaging.dll": {"related": ".xml"}}}, "Accord.MachineLearning/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0", "Accord.Math": "3.8.0", "Accord.Statistics": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.MachineLearning.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.MachineLearning.dll": {"related": ".xml"}}}, "Accord.Math/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.Math.Core.dll": {"related": ".xml"}, "lib/netstandard2.0/Accord.Math.dll": {"related": ".Core.xml;.xml"}}, "runtime": {"lib/netstandard2.0/Accord.Math.Core.dll": {"related": ".xml"}, "lib/netstandard2.0/Accord.Math.dll": {"related": ".Core.xml;.xml"}}}, "Accord.Statistics/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0", "Accord.Math": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.Statistics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.Statistics.dll": {"related": ".xml"}}}, "Accord.Video/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.Video.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.Video.dll": {"related": ".xml"}}}, "Accord.Vision/3.8.0": {"type": "package", "dependencies": {"Accord": "3.8.0", "Accord.Imaging": "3.8.0", "Accord.MachineLearning": "3.8.0", "Accord.Math": "3.8.0", "Accord.Statistics": "3.8.0", "Accord.Video": "3.8.0"}, "compile": {"lib/netstandard2.0/Accord.Vision.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Accord.Vision.dll": {"related": ".xml"}}}, "AForge/2.2.5": {"type": "package", "compile": {"lib/AForge.dll": {}}, "runtime": {"lib/AForge.dll": {}}}, "AForge.Video/2.2.5": {"type": "package", "dependencies": {"AForge": "2.2.5"}, "compile": {"lib/AForge.Video.dll": {}}, "runtime": {"lib/AForge.Video.dll": {}}}, "AForge.Video.DirectShow/2.2.5": {"type": "package", "dependencies": {"AForge.Video": "2.2.5"}, "compile": {"lib/AForge.Video.DirectShow.dll": {}}, "runtime": {"lib/AForge.Video.DirectShow.dll": {}}}, "ClosedXML/0.104.2": {"type": "package", "dependencies": {"ClosedXML.Parser": "[1.2.0, 2.0.0)", "DocumentFormat.OpenXml": "[3.1.1, 4.0.0)", "ExcelNumberFormat": "1.1.0", "RBush": "4.0.0", "SixLabors.Fonts": "1.0.0"}, "compile": {"lib/netstandard2.1/ClosedXML.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"related": ".pdb;.xml"}}}, "ClosedXML.Parser/1.2.0": {"type": "package", "compile": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "compile": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "dependencies": {"System.IO.Packaging": "8.0.1"}, "compile": {"lib/net6.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}}, "ExcelNumberFormat/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"related": ".xml"}}}, "Hardware.Info/101.0.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.PerformanceCounter": "8.0.0", "System.Management": "8.0.0"}, "compile": {"lib/netstandard2.0/Hardware.Info.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hardware.Info.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.VisualBasic/10.3.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "OpenCvSharp4/4.11.0.20250507": {"type": "package", "dependencies": {"System.Memory": "4.6.3"}, "compile": {"lib/net6.0/OpenCvSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/OpenCvSharp.dll": {"related": ".xml"}}}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"type": "package", "dependencies": {"OpenCvSharp4": "4.11.0.20250507", "System.Drawing.Common": "8.0.11"}, "compile": {"lib/net6.0/OpenCvSharp.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/OpenCvSharp.Extensions.dll": {"related": ".xml"}}}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"type": "package", "build": {"build/net5.0/OpenCvSharp4.runtime.win.props": {}}, "runtimeTargets": {"runtimes/win-x64/native/OpenCvSharpExtern.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg4110_64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/OpenCvSharpExtern.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/opencv_videoio_ffmpeg4110.dll": {"assetType": "native", "rid": "win-x86"}}}, "RBush/4.0.0": {"type": "package", "compile": {"lib/netstandard2.0/RBush.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/RBush.dll": {"related": ".xml"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "ScottPlot/4.1.68": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.2", "System.Numerics.Vectors": "4.5.0"}, "compile": {"lib/net6.0/ScottPlot.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/ScottPlot.dll": {"related": ".xml"}}}, "ScottPlot.WinForms/4.1.68": {"type": "package", "dependencies": {"ScottPlot": "4.1.68"}, "compile": {"lib/net6.0-windows7.0/ScottPlot.WinForms.dll": {}}, "runtime": {"lib/net6.0-windows7.0/ScottPlot.WinForms.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "SixLabors.Fonts/1.0.0": {"type": "package", "compile": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"related": ".xml"}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.SqlClient/4.9.0": {"type": "package", "dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "compile": {"lib/net6.0/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Data.SqlClient.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/8.0.11": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Packaging/8.0.1": {"type": "package", "compile": {"lib/net6.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.6.3": {"type": "package", "compile": {"lib/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Accord/3.8.0": {"sha512": "7kJrB570dO5ELim+2KWQNozuvWO9/BuZfZspdFy36fcWPNF2CEccblLuILeUlI8QJYd2DlBy0bfK8BlCx/uayA==", "type": "package", "path": "accord/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.3.8.0.nupkg.sha512", "accord.nuspec", "build/Accord.dll.config", "build/Accord.targets", "lib/net35-unity full v3.5/Accord.dll", "lib/net35-unity full v3.5/Accord.xml", "lib/net35-unity micro v3.5/Accord.dll", "lib/net35-unity micro v3.5/Accord.xml", "lib/net35-unity subset v3.5/Accord.dll", "lib/net35-unity subset v3.5/Accord.xml", "lib/net35-unity web v3.5/Accord.dll", "lib/net35-unity web v3.5/Accord.xml", "lib/net35/Accord.dll", "lib/net35/Accord.xml", "lib/net40/Accord.dll", "lib/net40/Accord.xml", "lib/net45/Accord.dll", "lib/net45/Accord.xml", "lib/net46/Accord.dll", "lib/net46/Accord.xml", "lib/net462/Accord.dll", "lib/net462/Accord.xml", "lib/netstandard1.4/Accord.dll", "lib/netstandard1.4/Accord.xml", "lib/netstandard2.0/Accord.dll", "lib/netstandard2.0/Accord.xml"]}, "Accord.Imaging/3.8.0": {"sha512": "PBZelpKoIW+h3uyNcTufFTCKxEtl/i8nNjgWctvvNu9EkEWDnZ0linckaDWr0rwO0Sx1DD8tL+hpxgOR1clgmg==", "type": "package", "path": "accord.imaging/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.imaging.3.8.0.nupkg.sha512", "accord.imaging.nuspec", "lib/net35-unity full v3.5/Accord.Imaging.dll", "lib/net35-unity full v3.5/Accord.Imaging.xml", "lib/net35-unity micro v3.5/Accord.Imaging.dll", "lib/net35-unity micro v3.5/Accord.Imaging.xml", "lib/net35-unity subset v3.5/Accord.Imaging.dll", "lib/net35-unity subset v3.5/Accord.Imaging.xml", "lib/net35-unity web v3.5/Accord.Imaging.dll", "lib/net35-unity web v3.5/Accord.Imaging.xml", "lib/net35/Accord.Imaging.dll", "lib/net35/Accord.Imaging.xml", "lib/net40/Accord.Imaging.dll", "lib/net40/Accord.Imaging.xml", "lib/net45/Accord.Imaging.dll", "lib/net45/Accord.Imaging.xml", "lib/net46/Accord.Imaging.dll", "lib/net46/Accord.Imaging.xml", "lib/net462/Accord.Imaging.dll", "lib/net462/Accord.Imaging.xml", "lib/netstandard2.0/Accord.Imaging.dll", "lib/netstandard2.0/Accord.Imaging.xml"]}, "Accord.MachineLearning/3.8.0": {"sha512": "6d8HY6/S4t5LBgD4YWrHISDX4q9s/caBu4fbHhq5RH2U4ETbtnDcvwgtsqGHKH1FyPylWFOniYjLO4B4xkEEuw==", "type": "package", "path": "accord.machinelearning/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.machinelearning.3.8.0.nupkg.sha512", "accord.machinelearning.nuspec", "lib/net35-unity full v3.5/Accord.MachineLearning.dll", "lib/net35-unity full v3.5/Accord.MachineLearning.xml", "lib/net35-unity micro v3.5/Accord.MachineLearning.dll", "lib/net35-unity micro v3.5/Accord.MachineLearning.xml", "lib/net35-unity subset v3.5/Accord.MachineLearning.dll", "lib/net35-unity subset v3.5/Accord.MachineLearning.xml", "lib/net35-unity web v3.5/Accord.MachineLearning.dll", "lib/net35-unity web v3.5/Accord.MachineLearning.xml", "lib/net35/Accord.MachineLearning.dll", "lib/net35/Accord.MachineLearning.xml", "lib/net40/Accord.MachineLearning.dll", "lib/net40/Accord.MachineLearning.xml", "lib/net45/Accord.MachineLearning.dll", "lib/net45/Accord.MachineLearning.xml", "lib/net46/Accord.MachineLearning.dll", "lib/net46/Accord.MachineLearning.xml", "lib/net462/Accord.MachineLearning.dll", "lib/net462/Accord.MachineLearning.xml", "lib/netstandard1.4/Accord.MachineLearning.dll", "lib/netstandard1.4/Accord.MachineLearning.xml", "lib/netstandard2.0/Accord.MachineLearning.dll", "lib/netstandard2.0/Accord.MachineLearning.xml"]}, "Accord.Math/3.8.0": {"sha512": "K3dzeQjDIrwRnoTRoMOoIbul2Uc0B8cnEtdrSlirmIo37C+jVkmYpJzme/z4Kg99XR2Vj5W5TTNlhjn+AKR+8A==", "type": "package", "path": "accord.math/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.math.3.8.0.nupkg.sha512", "accord.math.nuspec", "lib/net35-unity full v3.5/Accord.Math.Core.dll", "lib/net35-unity full v3.5/Accord.Math.Core.xml", "lib/net35-unity full v3.5/Accord.Math.dll", "lib/net35-unity full v3.5/Accord.Math.xml", "lib/net35-unity micro v3.5/Accord.Math.Core.dll", "lib/net35-unity micro v3.5/Accord.Math.Core.xml", "lib/net35-unity micro v3.5/Accord.Math.dll", "lib/net35-unity micro v3.5/Accord.Math.xml", "lib/net35-unity subset v3.5/Accord.Math.Core.dll", "lib/net35-unity subset v3.5/Accord.Math.Core.xml", "lib/net35-unity subset v3.5/Accord.Math.dll", "lib/net35-unity subset v3.5/Accord.Math.xml", "lib/net35-unity web v3.5/Accord.Math.Core.dll", "lib/net35-unity web v3.5/Accord.Math.Core.xml", "lib/net35-unity web v3.5/Accord.Math.dll", "lib/net35-unity web v3.5/Accord.Math.xml", "lib/net35/Accord.Math.Core.dll", "lib/net35/Accord.Math.Core.xml", "lib/net35/Accord.Math.dll", "lib/net35/Accord.Math.xml", "lib/net40/Accord.Math.Core.dll", "lib/net40/Accord.Math.Core.xml", "lib/net40/Accord.Math.dll", "lib/net40/Accord.Math.xml", "lib/net45/Accord.Math.Core.dll", "lib/net45/Accord.Math.Core.xml", "lib/net45/Accord.Math.dll", "lib/net45/Accord.Math.xml", "lib/net46/Accord.Math.Core.dll", "lib/net46/Accord.Math.Core.xml", "lib/net46/Accord.Math.dll", "lib/net46/Accord.Math.xml", "lib/net462/Accord.Math.Core.dll", "lib/net462/Accord.Math.Core.xml", "lib/net462/Accord.Math.dll", "lib/net462/Accord.Math.xml", "lib/netstandard1.4/Accord.Math.Core.dll", "lib/netstandard1.4/Accord.Math.Core.xml", "lib/netstandard1.4/Accord.Math.dll", "lib/netstandard1.4/Accord.Math.xml", "lib/netstandard2.0/Accord.Math.Core.dll", "lib/netstandard2.0/Accord.Math.Core.xml", "lib/netstandard2.0/Accord.Math.dll", "lib/netstandard2.0/Accord.Math.xml"]}, "Accord.Statistics/3.8.0": {"sha512": "8WsmCE31Qdy3FmRvwtAY3F9/fJEi/6uyLQrhOvSI6pP6gpoaacmCrJRIphkGf2EB8gKHRWLAc4UXr1OOPHxkmQ==", "type": "package", "path": "accord.statistics/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.statistics.3.8.0.nupkg.sha512", "accord.statistics.nuspec", "lib/net35-unity full v3.5/Accord.Statistics.dll", "lib/net35-unity full v3.5/Accord.Statistics.xml", "lib/net35-unity micro v3.5/Accord.Statistics.dll", "lib/net35-unity micro v3.5/Accord.Statistics.xml", "lib/net35-unity subset v3.5/Accord.Statistics.dll", "lib/net35-unity subset v3.5/Accord.Statistics.xml", "lib/net35-unity web v3.5/Accord.Statistics.dll", "lib/net35-unity web v3.5/Accord.Statistics.xml", "lib/net35/Accord.Statistics.dll", "lib/net35/Accord.Statistics.xml", "lib/net40/Accord.Statistics.dll", "lib/net40/Accord.Statistics.xml", "lib/net45/Accord.Statistics.dll", "lib/net45/Accord.Statistics.xml", "lib/net46/Accord.Statistics.dll", "lib/net46/Accord.Statistics.xml", "lib/net462/Accord.Statistics.dll", "lib/net462/Accord.Statistics.xml", "lib/netstandard1.4/Accord.Statistics.dll", "lib/netstandard1.4/Accord.Statistics.xml", "lib/netstandard2.0/Accord.Statistics.dll", "lib/netstandard2.0/Accord.Statistics.xml"]}, "Accord.Video/3.8.0": {"sha512": "efsmrydC22ZEisWkukqAiCtv27qF0l5/9U1eCCTkbnc5EYYE6F2RSnsfWr8iTELXb3ZsLLGO+Sy+K0NXrHDv4A==", "type": "package", "path": "accord.video/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.video.3.8.0.nupkg.sha512", "accord.video.nuspec", "lib/net35-unity full v3.5/Accord.Video.dll", "lib/net35-unity full v3.5/Accord.Video.xml", "lib/net35-unity micro v3.5/Accord.Video.dll", "lib/net35-unity micro v3.5/Accord.Video.xml", "lib/net35-unity subset v3.5/Accord.Video.dll", "lib/net35-unity subset v3.5/Accord.Video.xml", "lib/net35-unity web v3.5/Accord.Video.dll", "lib/net35-unity web v3.5/Accord.Video.xml", "lib/net35/Accord.Video.dll", "lib/net35/Accord.Video.xml", "lib/net40/Accord.Video.dll", "lib/net40/Accord.Video.xml", "lib/net45/Accord.Video.dll", "lib/net45/Accord.Video.xml", "lib/net46/Accord.Video.dll", "lib/net46/Accord.Video.xml", "lib/net462/Accord.Video.dll", "lib/net462/Accord.Video.xml", "lib/netstandard2.0/Accord.Video.dll", "lib/netstandard2.0/Accord.Video.xml"]}, "Accord.Vision/3.8.0": {"sha512": "efDpsPE7bu8cKckguxLORGLRBXD35lqHL4sEHuKNffBvFpnFrXmCXPPtzmh68AIk74hViQ37qLy3wPQ7YaYhAw==", "type": "package", "path": "accord.vision/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "accord.vision.3.8.0.nupkg.sha512", "accord.vision.nuspec", "lib/net35-unity full v3.5/Accord.Vision.dll", "lib/net35-unity full v3.5/Accord.Vision.xml", "lib/net35-unity micro v3.5/Accord.Vision.dll", "lib/net35-unity micro v3.5/Accord.Vision.xml", "lib/net35-unity subset v3.5/Accord.Vision.dll", "lib/net35-unity subset v3.5/Accord.Vision.xml", "lib/net35-unity web v3.5/Accord.Vision.dll", "lib/net35-unity web v3.5/Accord.Vision.xml", "lib/net35/Accord.Vision.dll", "lib/net35/Accord.Vision.xml", "lib/net40/Accord.Vision.dll", "lib/net40/Accord.Vision.xml", "lib/net45/Accord.Vision.dll", "lib/net45/Accord.Vision.xml", "lib/net46/Accord.Vision.dll", "lib/net46/Accord.Vision.xml", "lib/net462/Accord.Vision.dll", "lib/net462/Accord.Vision.xml", "lib/netstandard2.0/Accord.Vision.dll", "lib/netstandard2.0/Accord.Vision.xml"]}, "AForge/2.2.5": {"sha512": "clkumhM9DggqIzEXAHgVLeWO4arG5YfoPr7J4jfjJx35AoeEIJSSm49J25bwp/9mXQYLwi7y1Wunc8qgYJsGxg==", "type": "package", "path": "aforge/2.2.5", "files": [".nupkg.metadata", ".signature.p7s", "aforge.2.2.5.nupkg.sha512", "aforge.nuspec", "lib/AForge.dll", "lib/AForge.xml"]}, "AForge.Video/2.2.5": {"sha512": "XqzcOXtBUagEPRqg/00oayxlCPmxP4284SdM62mVotsNoD03fs19BrzdMBfhUOOYPyd0B/IXH7tEWnSDmc2gxA==", "type": "package", "path": "aforge.video/2.2.5", "files": [".nupkg.metadata", ".signature.p7s", "aforge.video.2.2.5.nupkg.sha512", "aforge.video.nuspec", "lib/AForge.Video.dll", "lib/AForge.Video.xml"]}, "AForge.Video.DirectShow/2.2.5": {"sha512": "pEch6felU/RGAbl0A7yjaQjsGxwiRFU9R+qBqR92wQo++XhzPLeQaZHnAPIBYaG7MfoqtjgCDkK4z3Tra4VQ3w==", "type": "package", "path": "aforge.video.directshow/2.2.5", "files": [".nupkg.metadata", ".signature.p7s", "aforge.video.directshow.2.2.5.nupkg.sha512", "aforge.video.directshow.nuspec", "lib/AForge.Video.DirectShow.dll", "lib/AForge.Video.DirectShow.xml"]}, "ClosedXML/0.104.2": {"sha512": "gOkSjQ152MhpKmw70cBkJV+FnaZAWzDwM36luRf/7FlWYnNeH++9XYdGTd0Y4KQlVPkKVxy948M5MMsnsGC4GQ==", "type": "package", "path": "closedxml/0.104.2", "files": [".nupkg.metadata", ".signature.p7s", "closedxml.0.104.2.nupkg.sha512", "closedxml.nuspec", "lib/netstandard2.0/ClosedXML.dll", "lib/netstandard2.0/ClosedXML.pdb", "lib/netstandard2.0/ClosedXML.xml", "lib/netstandard2.1/ClosedXML.dll", "lib/netstandard2.1/ClosedXML.pdb", "lib/netstandard2.1/ClosedXML.xml", "nuget-logo.png"]}, "ClosedXML.Parser/1.2.0": {"sha512": "w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "type": "package", "path": "closedxml.parser/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "closedxml.parser.1.2.0.nupkg.sha512", "closedxml.parser.nuspec", "lib/netstandard2.0/ClosedXML.Parser.dll", "lib/netstandard2.0/ClosedXML.Parser.xml", "lib/netstandard2.1/ClosedXML.Parser.dll", "lib/netstandard2.1/ClosedXML.Parser.xml"]}, "DocumentFormat.OpenXml/3.1.1": {"sha512": "2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "type": "package", "path": "documentformat.openxml/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.3.1.1.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/net8.0/DocumentFormat.OpenXml.dll", "lib/net8.0/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "DocumentFormat.OpenXml.Framework/3.1.1": {"sha512": "6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "type": "package", "path": "documentformat.openxml.framework/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.framework.3.1.1.nupkg.sha512", "documentformat.openxml.framework.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.Framework.dll", "lib/net35/DocumentFormat.OpenXml.Framework.xml", "lib/net40/DocumentFormat.OpenXml.Framework.dll", "lib/net40/DocumentFormat.OpenXml.Framework.xml", "lib/net46/DocumentFormat.OpenXml.Framework.dll", "lib/net46/DocumentFormat.OpenXml.Framework.xml", "lib/net6.0/DocumentFormat.OpenXml.Framework.dll", "lib/net6.0/DocumentFormat.OpenXml.Framework.xml", "lib/net8.0/DocumentFormat.OpenXml.Framework.dll", "lib/net8.0/DocumentFormat.OpenXml.Framework.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"]}, "ExcelNumberFormat/1.1.0": {"sha512": "R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "type": "package", "path": "excelnumberformat/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "excelnumberformat.1.1.0.nupkg.sha512", "excelnumberformat.nuspec", "icon.png", "lib/net20/ExcelNumberFormat.dll", "lib/net20/ExcelNumberFormat.xml", "lib/netstandard1.0/ExcelNumberFormat.dll", "lib/netstandard1.0/ExcelNumberFormat.xml", "lib/netstandard2.0/ExcelNumberFormat.dll", "lib/netstandard2.0/ExcelNumberFormat.xml"]}, "Hardware.Info/101.0.1": {"sha512": "eJaEk/6reCxXaatJYmJUjrv764LnWlkOhBHo+yOG2ZFLNYXwPIO1xzEiKtDckqXdNaw5YHgrT20C1GQ1r+WQRw==", "type": "package", "path": "hardware.info/101.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "hardware.info.101.0.1.nupkg.sha512", "hardware.info.nuspec", "lib/netstandard2.0/Hardware.Info.dll", "lib/netstandard2.0/Hardware.Info.xml"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.VisualBasic/10.3.0": {"sha512": "AvMDjmJHjz9bdlvxiSdEHHcWP+sZtp7zwule5ab6DaUbgoBnwCsd7nymj69vSz18ypXuEv3SI7ZUNwbIKrvtMA==", "type": "package", "path": "microsoft.visualbasic/10.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netcore50/Microsoft.VisualBasic.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.VisualBasic.dll", "lib/netstandard2.0/Microsoft.VisualBasic.dll", "lib/portable-net45+win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "microsoft.visualbasic.10.3.0.nupkg.sha512", "microsoft.visualbasic.nuspec", "ref/net45/_._", "ref/netcore50/Microsoft.VisualBasic.dll", "ref/netcore50/Microsoft.VisualBasic.xml", "ref/netcore50/de/Microsoft.VisualBasic.xml", "ref/netcore50/es/Microsoft.VisualBasic.xml", "ref/netcore50/fr/Microsoft.VisualBasic.xml", "ref/netcore50/it/Microsoft.VisualBasic.xml", "ref/netcore50/ja/Microsoft.VisualBasic.xml", "ref/netcore50/ko/Microsoft.VisualBasic.xml", "ref/netcore50/ru/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hans/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hant/Microsoft.VisualBasic.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/Microsoft.VisualBasic.dll", "ref/netstandard1.1/Microsoft.VisualBasic.xml", "ref/netstandard1.1/de/Microsoft.VisualBasic.xml", "ref/netstandard1.1/es/Microsoft.VisualBasic.xml", "ref/netstandard1.1/fr/Microsoft.VisualBasic.xml", "ref/netstandard1.1/it/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ja/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ko/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ru/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hans/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hant/Microsoft.VisualBasic.xml", "ref/netstandard2.0/Microsoft.VisualBasic.dll", "ref/netstandard2.0/Microsoft.VisualBasic.xml", "ref/portable-net45+win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "OpenCvSharp4/4.11.0.20250507": {"sha512": "j/R+G6xGC5IV2wGRU0/GF5qG/FrP+Uxp8dmNnXFlIdiw8Gfo4mtvKcBOBfS/bn4pP/7FNHLFX/xvMtgPJeDjAA==", "type": "package", "path": "opencvsharp4/4.11.0.20250507", "files": [".nupkg.metadata", ".signature.p7s", "lib/net48/OpenCvSharp.dll", "lib/net48/OpenCvSharp.xml", "lib/net6.0/OpenCvSharp.dll", "lib/net6.0/OpenCvSharp.xml", "lib/netstandard2.0/OpenCvSharp.dll", "lib/netstandard2.0/OpenCvSharp.xml", "lib/netstandard2.1/OpenCvSharp.dll", "lib/netstandard2.1/OpenCvSharp.xml", "opencvsharp4.4.11.0.20250507.nupkg.sha512", "opencvsharp4.nuspec"]}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"sha512": "xVA5xpgvI6cWBx+Zf+kL7NIVc8dpT6ScgHq3nV5tlMBpoG2aClKaLSFHtlPAE99ASfqNieaDzeOduJTe4V1aGw==", "type": "package", "path": "opencvsharp4.extensions/4.11.0.20250507", "files": [".nupkg.metadata", ".signature.p7s", "lib/net48/OpenCvSharp.Extensions.dll", "lib/net48/OpenCvSharp.Extensions.xml", "lib/net6.0/OpenCvSharp.Extensions.dll", "lib/net6.0/OpenCvSharp.Extensions.xml", "lib/netstandard2.0/OpenCvSharp.Extensions.dll", "lib/netstandard2.0/OpenCvSharp.Extensions.xml", "lib/netstandard2.1/OpenCvSharp.Extensions.dll", "lib/netstandard2.1/OpenCvSharp.Extensions.xml", "opencvsharp4.extensions.4.11.0.20250507.nupkg.sha512", "opencvsharp4.extensions.nuspec"]}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"sha512": "3PxMXyzR+pkL9UOx2PiBjZm+/iyCIu5D6OlA1sq9MH7oWrfvVnCXJlUYhIJv67F7SLUwUuGwXDIQBfdsL/54lg==", "type": "package", "path": "opencvsharp4.runtime.win/4.11.0.20250507", "files": [".nupkg.metadata", ".signature.p7s", "build/net48/OpenCvSharp4.runtime.win.props", "build/net5.0/OpenCvSharp4.runtime.win.props", "build/netcoreapp/OpenCvSharp4.runtime.win.props", "build/netstandard/OpenCvSharp4.runtime.win.props", "opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512", "opencvsharp4.runtime.win.nuspec", "runtimes/win-x64/native/OpenCvSharpExtern.dll", "runtimes/win-x64/native/opencv_videoio_ffmpeg4110_64.dll", "runtimes/win-x86/native/OpenCvSharpExtern.dll", "runtimes/win-x86/native/opencv_videoio_ffmpeg4110.dll"]}, "RBush/4.0.0": {"sha512": "j3GeRxxLUQdc+UrZnvythdQxi3bd8ayn87VDjfGXrvfodF550n9wR6SgQvpo+YiAv3GJezsu6lK0l47rRqnbdg==", "type": "package", "path": "rbush/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net47/RBush.dll", "lib/net47/RBush.xml", "lib/net8.0/RBush.dll", "lib/net8.0/RBush.xml", "lib/netstandard2.0/RBush.dll", "lib/netstandard2.0/RBush.xml", "rbush.4.0.0.nupkg.sha512", "rbush.nuspec", "readme.md"]}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "ScottPlot/4.1.68": {"sha512": "3ECJcKrX/VRv32kesrPRCH29q4+eHjZGxhsy09ssKBwif7WNoLs97xTYWFbpgq5Agwkk3M8k/GEQ4BZ06Vrsyg==", "type": "package", "path": "scottplot/4.1.68", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.ico", "icon.png", "lib/net462/ScottPlot.dll", "lib/net462/ScottPlot.xml", "lib/net6.0/ScottPlot.dll", "lib/net6.0/ScottPlot.xml", "lib/netstandard2.0/ScottPlot.dll", "lib/netstandard2.0/ScottPlot.xml", "scottplot.4.1.68.nupkg.sha512", "scottplot.nuspec"]}, "ScottPlot.WinForms/4.1.68": {"sha512": "amBcSIycxIxls+sn8eJduyBRutqvCJXom9f2njZnlL+oIvbmeAlsYGFiRhkcTDUClCch/mdK/4xHF/Nij9Gwpw==", "type": "package", "path": "scottplot.winforms/4.1.68", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net461/ScottPlot.WinForms.dll", "lib/net472/ScottPlot.WinForms.dll", "lib/net48/ScottPlot.WinForms.dll", "lib/net6.0-windows7.0/ScottPlot.WinForms.dll", "lib/netcoreapp3.1/ScottPlot.WinForms.dll", "scottplot.winforms.4.1.68.nupkg.sha512", "scottplot.winforms.nuspec"]}, "SixLabors.Fonts/1.0.0": {"sha512": "LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "type": "package", "path": "sixlabors.fonts/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.9.0": {"sha512": "j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "type": "package", "path": "system.data.sqlclient/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Data.SqlClient.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.SqlClient.targets", "lib/net462/System.Data.SqlClient.dll", "lib/net462/System.Data.SqlClient.xml", "lib/net6.0/System.Data.SqlClient.dll", "lib/net6.0/System.Data.SqlClient.xml", "lib/net8.0/System.Data.SqlClient.dll", "lib/net8.0/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net8.0/System.Data.SqlClient.xml", "runtimes/win/lib/net6.0/System.Data.SqlClient.dll", "runtimes/win/lib/net6.0/System.Data.SqlClient.xml", "runtimes/win/lib/net8.0/System.Data.SqlClient.dll", "runtimes/win/lib/net8.0/System.Data.SqlClient.xml", "system.data.sqlclient.4.9.0.nupkg.sha512", "system.data.sqlclient.nuspec"]}, "System.Diagnostics.PerformanceCounter/8.0.0": {"sha512": "lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.0.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/8.0.11": {"sha512": "AVyutHHKrX0Mt9C9T8W3Ccat3cVauUwvN+EqnVpSQi92nwBqoQ+2ZRvGP1S+rKK+6TGXmRflSYNShVjl2mMBlw==", "type": "package", "path": "system.drawing.common/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.11.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Packaging/8.0.1": {"sha512": "KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "type": "package", "path": "system.io.packaging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net6.0/System.IO.Packaging.dll", "lib/net6.0/System.IO.Packaging.xml", "lib/net7.0/System.IO.Packaging.dll", "lib/net7.0/System.IO.Packaging.xml", "lib/net8.0/System.IO.Packaging.dll", "lib/net8.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.8.0.1.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.6.3": {"sha512": "qdcDOgnFZY40+Q9876JUHnlHu7bosOHX8XISRoH94fwk6hgaeQGSgfZd8srWRZNt5bV9ZW2TljcegDNxsf+96A==", "type": "package", "path": "system.memory/4.6.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Memory.targets", "buildTransitive/net462/_._", "lib/net462/System.Memory.dll", "lib/net462/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "lib/netstandard2.1/_._", "system.memory.4.6.3.nupkg.sha512", "system.memory.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["AForge >= 2.2.5", "AForge.Video.DirectShow >= 2.2.5", "Accord >= 3.8.0", "Accord.Imaging >= 3.8.0", "Accord.Vision >= 3.8.0", "ClosedXML >= 0.104.2", "Hardware.Info >= 101.0.1", "Microsoft.VisualBasic >= 10.3.0", "OpenCvSharp4 >= 4.11.0.20250507", "OpenCvSharp4.Extensions >= 4.11.0.20250507", "OpenCvSharp4.runtime.win >= 4.11.0.20250507", "ScottPlot.WinForms >= 4.1.68", "System.Data.SqlClient >= 4.9.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj", "projectName": "EmployeeManagementSystem", "projectPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\EmployeeManagementSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"AForge": {"target": "Package", "version": "[2.2.5, )"}, "AForge.Video.DirectShow": {"target": "Package", "version": "[2.2.5, )"}, "Accord": {"target": "Package", "version": "[3.8.0, )"}, "Accord.Imaging": {"target": "Package", "version": "[3.8.0, )"}, "Accord.Vision": {"target": "Package", "version": "[3.8.0, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "Hardware.Info": {"target": "Package", "version": "[101.0.1, )"}, "Microsoft.VisualBasic": {"target": "Package", "version": "[10.3.0, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.Extensions": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.11.0.20250507, )"}, "ScottPlot.WinForms": {"target": "Package", "version": "[4.1.68, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'AForge 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "<PERSON><PERSON><PERSON><PERSON>", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'AForge.Video 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "AForge.Video", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'AForge.Video.DirectShow 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "AForge.Video.DirectShow", "targetGraphs": ["net6.0-windows7.0"]}]}