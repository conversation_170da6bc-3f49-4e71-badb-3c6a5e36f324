using System;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public static class EmployeeDepartmentHelper
    {
        // دالة للحصول على موظفي قسم معين
        public static DataTable GetEmployeesByDepartment(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees 
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users WHERE DepartmentId = @DepartmentId
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);
            return table;
        }

        // دالة للبحث في موظفي قسم معين
        public static DataTable SearchEmployeesInDepartment(string searchTerm, int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees 
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users WHERE DepartmentId = @DepartmentId
                )
                AND (
                    Name LIKE @SearchTerm OR 
                    MotherName LIKE @SearchTerm OR 
                    IdentityNumber LIKE @SearchTerm OR 
                    KeyCardNumber LIKE @SearchTerm OR 
                    PhoneNumber LIKE @SearchTerm OR
                    CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);
            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);
            return table;
        }

        // دالة لتطبيق فلترة مدير القسم على الموظفين
        public static DataTable GetFilteredEmployees(User? currentUser, string searchTerm = "")
        {
            if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    System.Diagnostics.Debug.WriteLine($"EmployeeDepartmentHelper - فلترة موظفي القسم {managedDepartmentId}");
                    return string.IsNullOrEmpty(searchTerm) ?
                        GetEmployeesByDepartment(managedDepartmentId.Value) :
                        SearchEmployeesInDepartment(searchTerm, managedDepartmentId.Value);
                }
                else
                {
                    // مدير قسم بدون قسم - إرجاع جدول فارغ
                    return new DataTable();
                }
            }
            else
            {
                // مدير عام أو مستخدم عادي - عرض جميع الموظفين
                return string.IsNullOrEmpty(searchTerm) ?
                    DatabaseHelper.GetAllEmployees() :
                    DatabaseHelper.SearchEmployees(searchTerm);
            }
        }
    }
}
