using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data.SqlClient;
using EmployeeManagementSystem.LoadingGui;
using System.Drawing.Drawing2D;

namespace EmployeeManagementSystem
{
    public partial class MainForm : Form
    {
        private Form? activeForm;
        private readonly System.Windows.Forms.Timer notificationTimer = new();
        private readonly Button notificationButton = new();
        private readonly Label notificationCounter = new();

        public string UserType { get; set; } = string.Empty;
        public string CurrentUsername { get; set; } = string.Empty;
        public User CurrentUser { get; set; } = new User();
        public MainForm(string userType)
        {
            InitializeComponent();
            try
            {
                UserType = userType;
                if (userType != "مدير")
                {
                    btnUsers.Visible = false;
                    btnActivityMonitor.Visible = false;
                }
                this.WindowState = FormWindowState.Maximized;
                ConfigureNotificationButton();
                ConfigureNotificationTimer();
                ApplySavedTheme();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل MainForm: " + ex.Message);
            }
        }

        //public MainForm(string userType)
        //{
        //    InitializeComponent();
        //    UserType = userType;
        //    if (userType != "مدير")
        //    {
        //        btnUsers.Visible = false;
        //    }
        //    // Configure main form
        //    this.WindowState = FormWindowState.Maximized;

        //    // Configure notification button
        //    ConfigureNotificationButton();

        //    // Configure notification timer
        //    ConfigureNotificationTimer();

        //    // Apply saved theme
        //    ApplySavedTheme();
        //}

        private void ConfigureNotificationButton()
        {
            notificationButton.Size = new Size(48, 48);
            notificationButton.FlatStyle = FlatStyle.Flat;
            notificationButton.FlatAppearance.BorderSize = 0;
            //notificationButton.Text = "🔔";
            notificationButton.Image = Properties.Resources.notification_32px;
            notificationButton.TextAlign = ContentAlignment.MiddleCenter;
            notificationButton.Font = new Font("Segoe UI Emoji", 15f);
            notificationButton.BackColor = Color.FromArgb(45, 66, 91);
            notificationButton.ForeColor = Color.White;
            notificationButton.Cursor = Cursors.Hand;
            notificationButton.Location = new Point(5, 5);
            notificationButton.Click += (s, e) => OpenForm(new NotificationsForm());

            // Configure notification counter
            notificationCounter.Size = new Size(20, 20);
            notificationCounter.BackColor = Color.Red;
            notificationCounter.ForeColor = Color.White;
            notificationCounter.Font = new Font("Cairo", 8f, FontStyle.Bold);
            notificationCounter.TextAlign = ContentAlignment.MiddleCenter;
            notificationCounter.Visible = false;

            // Position counter over bell icon
            int badgeX = (notificationButton.Width / 2) + 5;
            int badgeY = 2;
            notificationCounter.Location = new Point(badgeX, badgeY);

            // Make counter circular
            using GraphicsPath path = new();
            path.AddEllipse(0, 0, 20, 20);
            notificationCounter.Region = new Region(path);

            notificationButton.Controls.Add(notificationCounter);
            this.Controls.Add(notificationButton);
            notificationButton.BringToFront();
        }
        private void ConfigureNotificationTimer()
        {
            notificationTimer.Interval = 30000; // 30 seconds
            notificationTimer.Tick += async (s, e) =>
            {
                await UpdateNotificationCountAsync();
                await Task.Run(() =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"فحص الإشعارات في {DateTime.Now}");
                        NotificationService.CheckForUpcomingEvents();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في CheckForUpcomingEvents: {ex.Message}");
                    }
                });
            };
            notificationTimer.Start();

            // Initial notification check
            Task.Run(() =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"فحص أولي للإشعارات في {DateTime.Now}");
                    NotificationService.CheckForUpcomingEvents();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في CheckForUpcomingEvents: {ex.Message}");
                }
            });
        }

        private void ApplySavedTheme()
        {
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyTheme(theme);
            ThemeManager.ApplyThemeToForm(this);
        }


        // ... existing UpdateNotificationCountAsync and UpdateNotificationUI methods ...

        private void OpenForm(Form form)
        {
            try
            {
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    mainPanel.Controls.Clear();
                }

                activeForm = form;
                form.TopLevel = false;
                form.FormBorderStyle = FormBorderStyle.None;
                form.Dock = DockStyle.Fill;
                mainPanel.Controls.Add(form);

                // تطبيق الثيم على النموذج الجديد
                string theme = "default";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 Theme FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                theme = reader["Theme"]?.ToString() ?? "default";
                            }
                        }
                    }
                }
                catch
                {
                    theme = "default";
                }

                ThemeManager.ApplyThemeToForm(this);


                btnDashboard.Visible = false; // إخفاء الزر القديم
                form.Show();
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDashboard_Click(object sender, EventArgs e)
        {
            try
            {
                // Clear existing form if any
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    mainPanel.Controls.Clear();
                }

                // Create and configure new dashboard form
                var dashboardForm = new DashboardForm();
                dashboardForm.TopLevel = false;
                dashboardForm.FormBorderStyle = FormBorderStyle.None;
                dashboardForm.Dock = DockStyle.Fill;

                // Apply theme
                string theme = "default";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 Theme FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                theme = reader["Theme"]?.ToString() ?? "default";
                            }
                        }
                    }
                }
                catch
                {
                    theme = "default";
                }

                dashboardForm.UpdateTheme(theme);


                // Add and show dashboard
                mainPanel.Controls.Add(dashboardForm);
                activeForm = dashboardForm;
                dashboardForm.Show();

                // Update button appearance
                btnDashboard.BackColor = Color.FromArgb(87, 115, 153);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnManageEmployees_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new Form1());
                SetActiveButton(btnManageEmployees);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void BtnMonthlyOccurrences_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل نموذج الوقوعات الشهرية
                OpenForm(new MonthlyOccurrencesForm(CurrentUsername));
                SetActiveDropDownButton(btnWorkManagementMenu);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnVacations_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new VacationForm());
                SetActiveButton(btnVacations);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void BtnUsers_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب مع تمرير معلومات المستخدم الحالي
                OpenForm(new UserForm(CurrentUser));
                SetActiveButton(btnUsers);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void BtnActivityMonitor_Click(object sender, EventArgs e)
        {
            // التحقق من أن المستخدم مدير
            if (UserType != "مدير")
            {
                MessageBox.Show("هذه الميزة متاحة للمدير فقط", "غير مخول", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();

            try
            {
                // تسجيل الوصول إلى مراقب الأنشطة
                await ActivityLogService.LogActivityAsync(
                    "فتح مراقب الأنشطة",
                    "ActivityLogs",
                    null,
                    null,
                    null,
                    "استعلام",
                    "مهم",
                    "تم الوصول إلى مراقب الأنشطة من قبل المدير"
                );

                // فتح نموذج مراقبة الأنشطة
                var activityForm = new ActivityLogViewerForm(CurrentUser);
                activityForm.ShowDialog();
            }
            catch (Exception ex)
            {
                await ActivityLogService.LogFailedActivityAsync(
                    "فتح مراقب الأنشطة",
                    "ActivityLogs",
                    ex.Message
                );
                MessageBox.Show($"خطأ في فتح مراقب الأنشطة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private void BtnSettings_Click(object sender, EventArgs e)
        {
            //if (!ActivationHelper.CheckActivation())
            //    return;

            using SettingsForm settingsForm = new();
            if (settingsForm.ShowDialog() == DialogResult.OK)
            {
                // تطبيق الإعدادات الجديدة
                ApplySavedTheme();
            }
        }

        private async void BtnDepartments_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;

            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new DepartmentForm());
                SetActiveButton(btnDepartments);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }


        //if (!ActivationHelper.CheckActivation())
        //    return;


        private async void btnDepartments_Click_1(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;

            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            // تحميل الثيم وتطبيقه
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyTheme(theme);
            ThemeManager.ApplyThemeToForm(this);

            if (activeForm != null)
            {
                ThemeManager.ApplyThemeToForm(activeForm);
            }

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new DepartmentForm());
                SetActiveButton(btnDepartments);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    // تسجيل عملية الخروج
                    await ActivityLogService.LogLogoutAsync();
                }
                catch (Exception ex)
                {
                    // في حالة فشل تسجيل النشاط، نسجل الخطأ لكن لا نمنع تسجيل الخروج
                    System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل الخروج: {ex.Message}");
                }

                // إغلاق النموذج النشط
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    activeForm = null;
                }
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // إظهار نموذج تسجيل الدخول
                LoginForm loginForm = new();
                loginForm.Show();

                // إخفاء النموذج الحالي (نموذج الواجهة الرئيسية)
                this.Hide();
            }
        }

        private async void BtnHome_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new HomeForm(UserType, CurrentUsername));
                SetActiveButton(btnHome);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnCourseManagement_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnCourseAttendance_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseAttendanceForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnCourseEvaluation_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseEvaluationForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnCourseNotifications_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseNotificationsForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnCourseDetails_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseDetailsForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }

        }

        private async void BtnCourseStatistics_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);

            try
            {
                OpenForm(new CourseStatisticsForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private void SetActiveButton(ToolStripItem activeButton)
        {
            foreach (ToolStripItem item in toolStrip.Items)
            {
                if (item is ToolStripButton button)
                {
                    button.BackColor = button == activeButton ?
                        Color.FromArgb(87, 115, 153) :
                        Color.FromArgb(45, 66, 91);
                }
                else if (item is ToolStripDropDownButton dropDownButton)
                {
                    dropDownButton.BackColor = Color.FromArgb(45, 66, 91);
                }
            }
        }

        private void SetActiveDropDownButton(ToolStripDropDownButton activeDropDown)
        {
            foreach (ToolStripItem item in toolStrip.Items)
            {
                if (item is ToolStripButton button)
                {
                    button.BackColor = Color.FromArgb(45, 66, 91);
                }
                else if (item is ToolStripDropDownButton dropDownButton)
                {
                    dropDownButton.BackColor = dropDownButton == activeDropDown ?
                        Color.FromArgb(87, 115, 153) :
                        Color.FromArgb(45, 66, 91);
                }
            }
        }

        private async void AboutForm_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AboutForm());
                SetActiveButton(AboutForm);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void Activation_Click_1(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new activationForm());
                SetActiveButton(Activation);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            OpenForm(new HomeForm(UserType, CurrentUsername));

            // قم بتعيين الزر النشط
            SetActiveButton(btnHome);
            await UpdateNotificationCountAsync();
        }

        private async Task UpdateNotificationCountAsync()
        {
            try
            {
                int count = await Task.Run(() =>
                {
                    using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Notifications WHERE IsRead = 0";
                    using var command = new SqlCommand(query, connection);
                    return Convert.ToInt32(command.ExecuteScalar());
                });

                if (notificationCounter.InvokeRequired)
                {
                    notificationCounter.Invoke((MethodInvoker)delegate
                    {
                        UpdateNotificationUI(count);
                    });
                }
                else
                {
                    UpdateNotificationUI(count);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث عداد الإشعارات: {ex.Message}");
            }
        }
        private void UpdateNotificationUI(int count)
        {
            try
            {
                if (count > 0)
                {
                    notificationCounter.Text = count > 99 ? "99+" : count.ToString();
                    notificationCounter.Visible = true;
                }
                else
                {
                    notificationCounter.Visible = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة الإشعارات: {ex.Message}");
            }
        }
        //private async void NotificationTimer_Tick(object? sender, EventArgs e)
        //{
        //    await UpdateNotificationCountAsync();
        //    await Task.Run(() => NotificationService.CheckForUpcomingEvents());
        //}
        private async void NotificationTimer_Tick(object? sender, EventArgs e)
        {
            await UpdateNotificationCountAsync();

            await Task.Run(() =>
            {
                try
                {
                    NotificationService.CheckForUpcomingEvents();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في CheckForUpcomingEvents: {ex.Message}");
                }
            });
        }

        private async void BtnNotifications_Click(object sender, EventArgs e)
        {
            var notificationsForm = new NotificationsForm();
            notificationsForm.FormClosed += async (s, args) => await UpdateNotificationCountAsync();


            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة
            try
            {
                // تحميل النموذج المطلوب
                OpenForm(notificationsForm);
                SetActiveButton(btnNotifications);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnContacts_Click(object sender, EventArgs e)
        {
            var contactForm = new ContactForm();

            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة
            try
            {
                // تحميل النموذج المطلوب
                OpenForm(contactForm);
                SetActiveButton(btnContacts);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnDashboardStrip_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            await Task.Delay(100);

            try
            {
                var dashboardForm = new DashboardForm();
                OpenForm(dashboardForm);
                SetActiveButton(btnDashboardStrip);

                // جلب السمة من قاعدة البيانات
                string theme = "default";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 Theme FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                theme = reader["Theme"]?.ToString() ?? "default";
                            }
                        }
                    }
                }
                catch
                {
                    theme = "default";
                }

                dashboardForm.UpdateTheme(theme);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm.Close();
            }
        }


        private async void BtnAttendance_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AttendanceForm());
                SetActiveDropDownButton(btnAttendanceMenu);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnAttendanceReport_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AttendanceReportForm());
                SetActiveDropDownButton(btnAttendanceMenu);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnWorkPeriods_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new WorkPeriodForm());
                SetActiveDropDownButton(btnWorkManagementMenu);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnGroupWorkPeriods_Click(object sender, EventArgs e)
        {
            if (!ActivationHelper.CheckActivation())
                return;
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new GroupWorkPeriodForm());
                SetActiveDropDownButton(btnWorkManagementMenu);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }

       
    }


}