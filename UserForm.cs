using System;
using System.Windows.Forms;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public partial class UserForm : Form
    {
        private int? selectedUserId = null;
        public bool FromStartup { get; set; } = false;
        public bool IsFirstUserMode { get; set; } = false;
        private User currentUser = null;

        public UserForm()
        {
            InitializeComponent();
            LoadUsers();
            cmbUserType.Items.AddRange(new string[] { "مدير", "مدير القسم", "مستخدم" });

            // تحميل الأقسام
            LoadDepartments();

            // تطبيق الثيم المحفوظ
            string companyName = "المؤسسة";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 CompanyName FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            companyName = reader["CompanyName"]?.ToString() ?? "المؤسسة";
                        }
                    }
                }
            }
            catch
            {
                companyName = "المؤسسة";
            }

        }

        public UserForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // تسجيل للتتبع
            System.Diagnostics.Debug.WriteLine($"UserForm Constructor - تم استلام المستخدم:");
            System.Diagnostics.Debug.WriteLine($"  الاسم: {currentUser?.FullName}");
            System.Diagnostics.Debug.WriteLine($"  المعرف: {currentUser?.UserId}");
            System.Diagnostics.Debug.WriteLine($"  النوع: '{currentUser?.UserType}'");
            System.Diagnostics.Debug.WriteLine($"  القسم: {currentUser?.DepartmentId}");

            // تطبيق قيود مدير القسم
            if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
            {
                System.Diagnostics.Debug.WriteLine("تطبيق قيود مدير القسم...");

                // مدير القسم لا يستطيع تغيير نوع المستخدم إلى "مدير"
                cmbUserType.Items.Clear();
                cmbUserType.Items.AddRange(new string[] { "مدير القسم", "مستخدم" });

                // قفل القسم على قسم مدير القسم
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                System.Diagnostics.Debug.WriteLine($"القسم المُدار: {managedDepartmentId}");
                if (managedDepartmentId.HasValue)
                {
                    cmbDepartment.Enabled = false;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("المستخدم ليس مدير قسم أو البيانات فارغة");
            }
        }

        private void LoadUsers()
        {
            DataTable table;

            // تطبيق الفلترة حسب صلاحيات المستخدم
            if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
            {
                // إضافة تسجيل للتتبع
                System.Diagnostics.Debug.WriteLine($"مدير القسم: {currentUser.FullName} (ID: {currentUser.UserId})");
                System.Diagnostics.Debug.WriteLine($"نوع المستخدم: '{currentUser.UserType}'");
                System.Diagnostics.Debug.WriteLine($"قسم المستخدم: {currentUser.DepartmentId}");

                // البحث عن القسم الذي يديره هذا المستخدم
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                System.Diagnostics.Debug.WriteLine($"معرف القسم المُدار: {managedDepartmentId}");

                // تحديد القسم المناسب للفلترة
                int? targetDepartmentId = null;

                if (managedDepartmentId.HasValue)
                {
                    // إذا كان مديراً لقسم، استخدم ذلك القسم
                    targetDepartmentId = managedDepartmentId.Value;
                    System.Diagnostics.Debug.WriteLine($"استخدام القسم المُدار: {targetDepartmentId}");
                }
                else if (currentUser.DepartmentId.HasValue)
                {
                    // إذا لم يكن مديراً لقسم، استخدم قسمه الخاص
                    targetDepartmentId = currentUser.DepartmentId.Value;
                    System.Diagnostics.Debug.WriteLine($"استخدام قسم المستخدم: {targetDepartmentId}");
                }

                if (targetDepartmentId.HasValue)
                {
                    // مدير القسم يرى فقط مستخدمي قسمه
                    table = DatabaseHelper.GetUsersByDepartment(targetDepartmentId.Value);
                    System.Diagnostics.Debug.WriteLine($"عدد المستخدمين في القسم {targetDepartmentId}: {table.Rows.Count}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("لم يتم العثور على قسم - إرجاع جدول فارغ");
                    // إذا لم يكن له قسم، إرجاع جدول فارغ
                    table = new DataTable();
                    table.Columns.Add("رقم المستخدم", typeof(int));
                    table.Columns.Add("اسم المستخدم", typeof(string));
                    table.Columns.Add("الاسم الكامل", typeof(string));
                    table.Columns.Add("نوع المستخدم", typeof(string));
                    table.Columns.Add("معرف القسم", typeof(int));
                    table.Columns.Add("القسم", typeof(string));
                }
            }
            else
            {
                // المدير يرى جميع المستخدمين
                System.Diagnostics.Debug.WriteLine($"مستخدم عادي أو مدير: {currentUser?.FullName} - نوع: '{currentUser?.UserType}'");
                table = DatabaseHelper.GetAllUsers();
            }

            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "UserId":
                        column.ColumnName = "رقم المستخدم";
                        break;
                    case "Username":
                        column.ColumnName = "اسم المستخدم";
                        break;
                    case "FullName":
                        column.ColumnName = "الاسم الكامل";
                        break;
                    case "UserType":
                        column.ColumnName = "نوع المستخدم";
                        break;
                    case "DepartmentName":
                        column.ColumnName = "القسم";
                        break;
                    case "DepartmentId":
                        column.ColumnName = "معرف القسم";
                        break;
                }
            }

            dataGridView1.DataSource = table;
            dataGridView1.Refresh();

            // إخفاء عمود كلمة المرور لأسباب أمنية
            if (dataGridView1.Columns.Contains("Password"))
                dataGridView1.Columns["Password"].Visible = false;
        }

        //private void btnAdd_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        if (string.IsNullOrWhiteSpace(txtUsername.Text) ||
        //            string.IsNullOrWhiteSpace(txtFullName.Text) ||
        //            string.IsNullOrWhiteSpace(txtPassword.Text) ||
        //            string.IsNullOrWhiteSpace(cmbUserType.Text))
        //        {
        //            MessageBox.Show("الرجاء إدخال جميع البيانات المطلوبة", "تنبيه",
        //                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //            return;
        //        }

        //        var user = new User
        //        {
        //            Username = txtUsername.Text,
        //            FullName = txtFullName.Text,
        //            Password = txtPassword.Text,
        //            UserType = cmbUserType.Text
        //        };

        //        DatabaseHelper.AddUser(user);
        //        MessageBox.Show("تمت إضافة المستخدم بنجاح", "نجاح",
        //            MessageBoxButtons.OK, MessageBoxIcon.Information);

        //        if (FromStartup) // ← فقط عند أول تشغيل
        //        {
        //            this.DialogResult = DialogResult.OK;
        //            this.Close();
        //        }
        //        else
        //        {
        //            LoadUsers();
        //            ToastHelper.ShowAddToast();
        //            ClearFields();
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
        //            MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text) ||
                    string.IsNullOrWhiteSpace(txtFullName.Text) ||
                    string.IsNullOrWhiteSpace(txtPassword.Text) ||
                    string.IsNullOrWhiteSpace(cmbUserType.Text) ||
                    cmbDepartment.SelectedItem == null)  // تأكد من اختيار القسم
                {
                    MessageBox.Show("الرجاء إدخال جميع البيانات المطلوبة واختيار القسم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int selectedDepartmentId;

                // إذا كان المستخدم الحالي مدير قسم، فرض قسمه
                if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
                {
                    int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);

                    if (managedDepartmentId.HasValue)
                    {
                        selectedDepartmentId = managedDepartmentId.Value;
                    }
                    else if (currentUser.DepartmentId.HasValue)
                    {
                        selectedDepartmentId = currentUser.DepartmentId.Value;
                    }
                    else
                    {
                        MessageBox.Show("لم يتم تعيين قسم لك. يرجى التواصل مع المدير العام.", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }
                else
                {
                    selectedDepartmentId = ((DepartmentItem)cmbDepartment.SelectedItem).DepartmentId;
                }

                var user = new User
                {
                    Username = txtUsername.Text,
                    FullName = txtFullName.Text,
                    Password = txtPassword.Text,
                    UserType = cmbUserType.Text,
                    DepartmentId = selectedDepartmentId  // ← أضف القسم هنا
                };

                DatabaseHelper.AddUser(user);

                MessageBox.Show("تمت إضافة المستخدم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                if (FromStartup)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    LoadUsers();
                    ToastHelper.ShowAddToast();
                    ClearFields();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        //private void btnUpdate_Click(object sender, EventArgs e)
        //{
        //    if (!selectedUserId.HasValue)
        //    {
        //        MessageBox.Show("الرجاء اختيار مستخدم للتعديل", "تنبيه",
        //            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        return;
        //    }

        //    try
        //    {
        //        var user = new User
        //        {
        //            UserId = selectedUserId.Value,
        //            Username = txtUsername.Text,
        //            FullName = txtFullName.Text,
        //            Password = string.IsNullOrWhiteSpace(txtPassword.Text) ?
        //                      null : txtPassword.Text, // تحديث كلمة المرور فقط إذا تم إدخالها
        //            UserType = cmbUserType.Text
        //        };

        //        DatabaseHelper.UpdateUser(user);
        //        MessageBox.Show("تم تعديل المستخدم بنجاح", "نجاح",
        //            MessageBoxButtons.OK, MessageBoxIcon.Information);

        //        LoadUsers();
        //        ToastHelper.ShowEditToast();
        //        ClearFields();
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
        //            MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!selectedUserId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                int? selectedDepartmentId = null;

                // إذا كان المستخدم الحالي مدير قسم، فرض قسمه
                if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
                {
                    int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);

                    if (managedDepartmentId.HasValue)
                    {
                        selectedDepartmentId = managedDepartmentId.Value;
                    }
                    else if (currentUser.DepartmentId.HasValue)
                    {
                        selectedDepartmentId = currentUser.DepartmentId.Value;
                    }
                    else
                    {
                        MessageBox.Show("لم يتم تعيين قسم لك. يرجى التواصل مع المدير العام.", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }
                else if (cmbDepartment.SelectedItem != null)
                {
                    selectedDepartmentId = ((DepartmentItem)cmbDepartment.SelectedItem).DepartmentId;
                }

                var user = new User
                {
                    UserId = selectedUserId.Value,
                    Username = txtUsername.Text,
                    FullName = txtFullName.Text,
                    Password = string.IsNullOrWhiteSpace(txtPassword.Text) ?
                              null : txtPassword.Text, // تحديث كلمة المرور فقط إذا تم إدخالها
                    UserType = cmbUserType.Text,
                    DepartmentId = selectedDepartmentId  // أضف القسم هنا
                };

                DatabaseHelper.UpdateUser(user);
                MessageBox.Show("تم تعديل المستخدم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadUsers();
                ToastHelper.ShowEditToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (!selectedUserId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("هل أنت متأكد من حذف هذا المستخدم؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteUser(selectedUserId.Value);
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجاح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LoadUsers();
                    ToastHelper.ShowDeleteToast();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentRow != null)
            {
                try
                {
                    selectedUserId = Convert.ToInt32(dataGridView1.CurrentRow.Cells["رقم المستخدم"].Value);
                    if (selectedUserId.HasValue)
                    {
                        var user = DatabaseHelper.GetUserById(selectedUserId.Value);
                        txtUsername.Text = user.Username;
                        txtFullName.Text = user.FullName;
                        txtPassword.Text = ""; // لا نعرض كلمة المرور لأسباب أمنية
                        cmbUserType.Text = user.UserType;
                        if (user.DepartmentId != null)
                        {
                            int departmentId = user.DepartmentId.Value;
                            foreach (var item in cmbDepartment.Items)
                            {
                                if (item is DepartmentItem deptItem && deptItem.DepartmentId == departmentId)
                                {
                                    cmbDepartment.SelectedItem = item;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            cmbDepartment.SelectedIndex = -1; // لا يوجد قسم معين
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        private void ClearFields()
        {
            selectedUserId = null;
            txtUsername.Clear();
            txtFullName.Clear();
            txtPassword.Clear();
            cmbUserType.SelectedIndex = -1;
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }


        private void LoadDepartments()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT DepartmentId, DepartmentName FROM Departments ORDER BY DepartmentName";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        using var reader = command.ExecuteReader();
                        cmbDepartment.Items.Clear();
                        while (reader.Read())
                        {
                            string departmentName = reader["DepartmentName"].ToString() ?? "";
                            int departmentId = Convert.ToInt32(reader["DepartmentId"]);

                            // إنشاء كائن مخصص لتخزين اسم القسم ومعرفه
                            var item = new DepartmentItem(departmentId, departmentName);
                            cmbDepartment.Items.Add(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأقسام: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // فئة لتخزين معلومات القسم
        private class DepartmentItem
        {
            public int DepartmentId { get; set; }
            public string DepartmentName { get; set; }

            public DepartmentItem(int id, string name)
            {
                DepartmentId = id;
                DepartmentName = name;
            }

            public override string ToString()
            {
                return DepartmentName;
            }
        }


        private void UserForm_Load(object sender, EventArgs e)
        {
            LoadDepartments();

            // ضبط القسم المحدد لمدير القسم
            if (currentUser != null && (currentUser.UserType == "مدير القسم" || currentUser.UserType == "مدير قسم"))
            {
                int? targetDepartmentId = null;
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);

                if (managedDepartmentId.HasValue)
                {
                    targetDepartmentId = managedDepartmentId.Value;
                }
                else if (currentUser.DepartmentId.HasValue)
                {
                    targetDepartmentId = currentUser.DepartmentId.Value;
                }

                if (targetDepartmentId.HasValue)
                {
                    foreach (var item in cmbDepartment.Items)
                    {
                        if (item is DepartmentItem deptItem && deptItem.DepartmentId == targetDepartmentId.Value)
                        {
                            cmbDepartment.SelectedItem = item;
                            break;
                        }
                    }
                }
            }

            if (IsFirstUserMode)
            {
                // إخفاء كل شيء غير ضروري
                btnUpdate.Visible = false;
                btnDelete.Visible = false;
                dataGridView1.Visible = false;
                btnClear.Visible = false; // إذا كنت تريده يظهر عند أول تشغيل

                // قفل نوع المستخدم على "مدير"
                cmbUserType.SelectedItem = "مدير";
                cmbUserType.Enabled = false;

                // مسح الحقول
                txtUsername.Clear();
                txtFullName.Clear();
                txtPassword.Clear();
                cmbUserType.SelectedIndex = 0;
            }
            else
            {
                // الوضع العادي
                LoadUsers(); // ← فقط إذا ليس في الوضع الأول
            }
            btnClose.Visible = FromStartup;
        }
    }
}