using System;
using System.Windows.Forms;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public partial class DepartmentForm : Form
    {
        private int? selectedDepartmentId = null;
        public int? AdminUserId { get; set; }

        public DepartmentForm()
        {
            InitializeComponent();
            LoadDepartments();
        }

        private void LoadDepartments()
        {
            var table = DatabaseHelper.GetAllDepartments();

            // تغيير أسماء الأعمدة للعرض بالعربية
            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "DepartmentId":
                        column.ColumnName = "رقم القسم";
                        break;
                    case "DepartmentName":
                        column.ColumnName = "اسم القسم";
                        break;
                    case "DepartmentCode":
                        column.ColumnName = "رمز القسم";
                        break;
                    case "Description":
                        column.ColumnName = "الوصف";
                        break;
                    case "ManagerUserId":
                        column.ColumnName = "مدير القسم";
                        break;
                    case "CreatedDate":
                        column.ColumnName = "تاريخ الإنشاء";
                        break;
                    case "CreatedBy":
                        column.ColumnName = "أنشئ بواسطة";
                        break;
                    case "IsActive":
                        column.ColumnName = "نشط";
                        break;
                }
            }

            // إضافة عمود لعرض اسم مدير القسم بدلاً من الرقم
            AddManagerNameColumn(table);

            dataGridView1.DataSource = table;
            dataGridView1.Refresh();
        }

        private void AddManagerNameColumn(DataTable table)
        {
            // إضافة عمود جديد لاسم مدير القسم
            table.Columns.Add("اسم مدير القسم", typeof(string));

            foreach (DataRow row in table.Rows)
            {
                if (!row.IsNull("مدير القسم"))
                {
                    int managerId = Convert.ToInt32(row["مدير القسم"]);
                    try
                    {
                        var manager = DatabaseHelper.GetUserById(managerId);
                        row["اسم مدير القسم"] = manager.FullName;
                    }
                    catch
                    {
                        row["اسم مدير القسم"] = "غير معروف";
                    }
                }
                else
                {
                    row["اسم مدير القسم"] = "غير معين";
                }
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtDepartmentName.Text) ||
                    string.IsNullOrWhiteSpace(txtDepartmentCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم القسم ورمزه", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var department = new Department
                {
                    DepartmentName = txtDepartmentName.Text,
                    DepartmentCode = txtDepartmentCode.Text,
                    Description = txtDescription.Text,
                    CreatedDate = DateTime.Now,
                    CreatedBy = AdminUserId.HasValue ? AdminUserId.Value : 1, // استخدام معرف المستخدم الحالي
                    IsActive = chkIsActive.Checked
                };

                DatabaseHelper.AddDepartment(department);
                MessageBox.Show("تمت إضافة القسم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadDepartments();
                ToastHelper.ShowAddToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!selectedDepartmentId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار قسم للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var department = new Department
                {
                    DepartmentId = selectedDepartmentId.Value,
                    DepartmentName = txtDepartmentName.Text,
                    DepartmentCode = txtDepartmentCode.Text,
                    Description = txtDescription.Text,
                    IsActive = chkIsActive.Checked
                };

                DatabaseHelper.UpdateDepartment(department);
                MessageBox.Show("تم تعديل القسم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadDepartments();
                ToastHelper.ShowEditToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (!selectedDepartmentId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار قسم للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("هل أنت متأكد من حذف هذا القسم؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteDepartment(selectedDepartmentId.Value);
                    MessageBox.Show("تم حذف القسم بنجاح", "نجاح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LoadDepartments();
                    ToastHelper.ShowDeleteToast();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentRow != null)
            {
                try
                {
                    selectedDepartmentId = Convert.ToInt32(dataGridView1.CurrentRow.Cells["رقم القسم"].Value);
                    if (selectedDepartmentId.HasValue)
                    {
                        var department = DatabaseHelper.GetDepartmentById(selectedDepartmentId.Value);
                        txtDepartmentName.Text = department.DepartmentName;
                        txtDepartmentCode.Text = department.DepartmentCode;
                        txtDescription.Text = department.Description;
                        chkIsActive.Checked = department.IsActive;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        private void ClearFields()
        {
            selectedDepartmentId = null;
            txtDepartmentName.Clear();
            txtDepartmentCode.Clear();
            txtDescription.Clear();
            chkIsActive.Checked = true;
        }

        private void DepartmentForm_Load(object sender, EventArgs e)
        {
            LoadDepartments();
        }
    }
}