-- Script لإعداد نظام الصلاحيات لمدراء الأقسام
-- تشغيل هذا الـ script في SQL Server Management Studio

-- 1. التحقق من الأقسام الموجودة
SELECT 
    DepartmentId,
    DepartmentName,
    DepartmentCode,
    ManagerUserId,
    IsActive
FROM Departments
ORDER BY DepartmentName;

-- 2. التحقق من المستخدمين الموجودين
SELECT 
    UserId,
    Username,
    FullName,
    UserType,
    DepartmentId
FROM Users
ORDER BY UserType, FullName;

-- 3. مثال: إنشاء قسم جديد
INSERT INTO Departments (DepartmentName, DepartmentCode, Description, CreatedDate, CreatedBy, IsActive)
VALUES ('قسم المحاسبة', 'ACC', 'قسم المحاسبة والشؤون المالية', GETDATE(), 1, 1);

-- 4. مثال: إنشاء مدير قسم جديد
-- (يجب تشغيل هذا من خلال البرنامج لضمان تشفير كلمة المرور)
-- INSERT INTO Users (Username, FullName, Password, UserType, DepartmentId)
-- VALUES ('manager_acc', 'أحمد محمد - مدير المحاسبة', 'password123', 'مدير القسم', 1);

-- 5. ربط مدير القسم بقسمه
-- استبدل الأرقام بالقيم الصحيحة من قاعدة البيانات
-- UPDATE Departments 
-- SET ManagerUserId = [رقم_المستخدم_مدير_القسم]
-- WHERE DepartmentId = [رقم_القسم];

-- مثال:
-- UPDATE Departments 
-- SET ManagerUserId = 3 
-- WHERE DepartmentId = 1;

-- 6. التحقق من الإعداد
SELECT 
    d.DepartmentId,
    d.DepartmentName,
    d.ManagerUserId,
    u.Username as ManagerUsername,
    u.FullName as ManagerFullName,
    u.UserType
FROM Departments d
LEFT JOIN Users u ON d.ManagerUserId = u.UserId
WHERE d.IsActive = 1
ORDER BY d.DepartmentName;

-- 7. عرض المستخدمين في كل قسم
SELECT 
    d.DepartmentName,
    u.Username,
    u.FullName,
    u.UserType,
    CASE 
        WHEN d.ManagerUserId = u.UserId THEN 'مدير القسم'
        ELSE 'موظف'
    END as Role
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.DepartmentId
ORDER BY d.DepartmentName, u.FullName;

-- 8. التحقق من مدراء الأقسام الذين لم يتم تعيين أقسام لهم
SELECT 
    u.UserId,
    u.Username,
    u.FullName,
    u.UserType,
    u.DepartmentId,
    d.DepartmentName
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.DepartmentId
WHERE u.UserType = 'مدير القسم'
AND NOT EXISTS (
    SELECT 1 FROM Departments dept 
    WHERE dept.ManagerUserId = u.UserId AND dept.IsActive = 1
);

-- 9. إصلاح مدراء الأقسام غير المعينين
-- (مثال: تعيين مدير القسم للقسم الذي ينتمي إليه)
/*
UPDATE Departments 
SET ManagerUserId = u.UserId
FROM Departments d
INNER JOIN Users u ON d.DepartmentId = u.DepartmentId
WHERE u.UserType = 'مدير القسم'
AND d.ManagerUserId IS NULL
AND d.IsActive = 1;
*/
